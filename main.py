import sys
from PyQt5.QtWidgets import *
from PyQt5.QtGui import *
from PyQt5.QtCore import *
# from PyQt5.QtUiTools import *
from PyQt5.uic import loadUi
from PyQt5.QtCore import QObject, pyqtSignal
import Light_Control
from order import getOrder
import serial
from serial.tools import list_ports
import matplotlib.pyplot as plt
from threading import Thread
import numpy as np
import time
import json
import os
from colorTrans import RGBToHTMLColor, wavelength_to_rgb
from linear import getFunc
import pyqtgraph as pg
import csv
from scipy.interpolate import interp1d
from match_old import m_matchSpd
# from match_old import m_matchSpd
import inspect
import ctypes
from license import read_dog_stats
import psutil, os
import write_data
import Spectrometer_Control
import Spectrometer_Control_IR
import measure_math_control
# from PyQt5.QtCore import *
# from PyQt5.QtGui import *
# from PyQt5.QtWidgets import *
# from PyQt5.QtWidgets import QMainWindow, QApplication
# from PyQt5.QtCore import *
# from PyQt5.QtGui import *
# from PyQt5.QtWidgets import *
# from PyQt5 import QtCore, QtGui, QtWidgets
from openpyxl import load_workbook

# 反射光源控制
import light_api
import pydmx

# 光谱校正功能
from correction import SpectrumCorrection, save_correction_log


def kill_proc_tree(pid, including_parent=True):
    parent = psutil.Process(pid)
    for child in parent.children(recursive=True):
        child.kill()
    if including_parent:
        parent.kill()


class Signal_View(QObject):
    error = pyqtSignal(str, str)
    note = pyqtSignal(str, str)
    warning = pyqtSignal(str, str)
    enable_correction = pyqtSignal()  # 启用校正按钮的信号


class PyDMX:  ##Coppied from the internet. This does the sending to dmx stuff
    # def __init__(self, COM='COM7', Brate=250000, Bsize=8, StopB=2):
    #     # start serial
    #     self.working = True
    #     try:
    #         self.ser = serial.Serial(COM, baudrate=Brate, bytesize=Bsize, stopbits=StopB)
    #     except:
    #         self.working = False
    #         print(
    #             "The serial is not working. Please make sure the lights are plugged in and that you don't have any other dmx scripts running and make sure the port is correct")
    #         # while True:
    #         #     print("Please close and restart the program")
    #     self.data = np.zeros([513], dtype='uint8')
    #     self.data[0] = 0  # StartCode
    #     self.sleepms = 50.0
    #     self.breakus = 176.0
    #     self.MABus = 16.0

    def connect_dmx(self, COM='COM7'):
        Brate = 250000
        Bsize = 8
        StopB = 2
        try:
            self.ser = serial.Serial(COM, baudrate=Brate, bytesize=Bsize, stopbits=StopB)
            print('COM:', COM)
            print('Connect Ok')
        except:
            # self.working = False
            print(
                "The serial is not working. Please make sure the lights are plugged in and that you don't have any other dmx scripts running and make sure the port is correct")
            # while True:
            #     print("Please close and restart the program")
        self.data = np.zeros([513], dtype='uint8')
        self.data[0] = 0  # StartCode
        self.sleepms = 50.0
        self.breakus = 176.0
        self.MABus = 16.0

    def set_random_data(self):
        self.data[1:513] = np.random.rand(512) * 255

    def set_data(self, id, data):
        self.data[id] = data
        self.send()

    def send(self):
        # Send Break : 88us - 1s
        self.ser.break_condition = True
        time.sleep(self.breakus / 1000000.0)

        # Send MAB : 8us - 1s
        self.ser.break_condition = False
        time.sleep(self.MABus / 1000000.0)

        # Send Data
        self.ser.write(bytearray(self.data))
        # print(self.data)

        # Sleep
        time.sleep(self.sleepms / 1000.0)  # between 0 - 1 sec

    def sendzero(self):
        self.data = np.zeros([513], dtype='uint8')
        self.send()


class Stats:
    def __init__(self):
        self.ui = loadUi('./ui/mainwindow.ui')
        self.init_window_signal()
        self.ui.colorspace_logo.setPixmap(QPixmap(os.getcwd() + "/ui/Source/logo.png"))

        # 无需加密版本
        # if read_dog_stats() == "multiLight":
        #   print('Success!')
        # else:
        #     # pass
        #     self.signal.error.emit("权限", "请检查加密狗!")
        #     return

        # 界面列表
        self.spd_widget_list = []
        self.spd_checkBox_list = []
        self.spd_slider_list = []
        self.spd_lineEdit_list = []
        # 串口列表
        self.serials_names = []
        # led，array原生数据列表
        self.led_data_arrays = []
        # led 最大通道的array,shape->(-1,self.spd_num)
        self.led_data_max_channel = []
        # 线性函数
        self.linear_func = []
        # 线性电流
        self.linear_current = []
        # 线性亮度
        self.linear_light = []
        # 校正后电流
        self.linear_current_d = []
        # 通信串口
        self.serial = None
        self.serial_2 = None
        self.serial_3 = None
        self.serial_4 = None
        self.serial_5 = None
        self.serial_6 = None

        # 当前串口
        self.current_serial = "COM3"
        # 通信协议字典
        self.protocol = None
        # 当前协议
        self.current_protocol = None
        # 标准光谱
        self.std_ledData_dict = None
        # 目标光谱
        self.target_spd = None

        self.slider_draw = True
        # 当前光谱曲线

        self.current_wave = []
        self.match_wave = []
        self.measure_wave = []  # 测量的原始数据
        self.measure_wave_1 = []  # 测量的归一化处理后数据
        # button->np.savetxt("result.csv",self.measure_wave,delimiter=",")

        # 校正功能相关变量
        self.correction_enabled = False  # 校正按钮是否可用

        self.read_setting()
        self.init_spd_num()
        self.init_protocol()

        self.init_btn_connect()

        self.init_spd_widget_list()
        self.init_spd_checkBox_list()
        self.init_spd_slider_list()
        self.init_spd_lineEdit_list()

        self.init_spd_slider_change()
        self.init_spd_lineEdit_press()
        self.init_spd_checkBox_enable()

        self.init_comboBox_ledData()
        self.watch_serial_port()

        # self.serial_2 = 'com1'
        # self.serial_3 = 'com1'
        # self.serial_4 = 'com1'
        # self.serial_5 = 'com1'
        # self.serial_6 = 'com1'

        self.init_QtGraph()

        self.init_ratioButtonGroup()

        # todo 保存当前数据 Cyh
        self.ui.pushButton_light_status.setStyleSheet("background-color:#FF0000")  #
        self.ui.pushButton_cs_lm01_status.setStyleSheet("background-color:#FF0000")
        self.pushButton_saveOrder_click()
        # 初始化表格
        self.init_tableWidget()
        self.pushButton_sendLight_click()
        self.pushButton_saveOrder_to_light_click()
        # 按钮绑定事件 self.ui.pushButton_matchStart.clicked.connect(matchSpd)

        # Flicker 功能
        self.pushButton_sendflickLight_click()

        # 照度计连接
        self.spec_serials_connect_click()

        # LOOP功能
        self.pushButton_loop_import_click()
        # 开始运行LOOP
        # self.pushButton_loop_run_click()

        self.pushButton_loop_stop_click()
        self.pushButton_loop_save_click()

        # self.ui.tl84_spinBox.setM
        self.ui.radioButton_send_true.setChecked(True)

        # 展示默认UI
        # self.ui.pushButton_le007_left_switch.setStyleSheet(
        #     'QPushButton{border-image: url(./ui/switch_new_off.png)}')
        # self.ui.pushButton_le007_right_switch.setStyleSheet(
        #     'QPushButton{border-image: url(./ui/switch_new_off.png)}')

        self.pushButton_importOrder_2_click()
        self.dmx = PyDMX()

        # self.spd_num = self.setting["channels_num"]
        print("self.setting:", self.setting["light_com1"])

        light1_com = self.setting["light_com1"]
        print(type(light1_com))
        self.ui.comboBox_serials.setCurrentText(light1_com)
        self.ui.comboBox_serials_2.setCurrentText(self.setting["light_com2"])
        self.ui.comboBox_serials_3.setCurrentText(self.setting["light_com3"])
        self.ui.comboBox_serials_4.setCurrentText(self.setting["light_com4"])
        self.ui.comboBox_serials_5.setCurrentText(self.setting["light_com5"])
        self.ui.comboBox_serials_6.setCurrentText(self.setting["light_com6"])

        # self.ui.checkBox_ch2.setChecked(True)
        # self.ui.checkBox_ch3.setChecked(True)

        # 初始化校正按钮为禁用状态
        self.ui.pushButton_correction.setEnabled(False)

    def read_setting(self):
        with open("setting.json", 'r', encoding='utf-8') as f:
            self.setting = json.load(f)

        self.std_ledData_dict = self.setting["std_data"]

    def init_spd_num(self):
        # 从硬件读取灯珠信息，改变界面，目前软件可支持到36通道
        self.spd_num = self.setting["channels_num"]
        str_correct = ""
        for i in range(self.spd_num):
            str_correct += str(i + 1) + ","
        str_correct += str(self.spd_num + 1)

    # self.ui.lineEdit_channels.setText(str_correct)

    # Todo 照度计连接
    def spec_serials_connect_click(self):
        def connect():
            Spectrometer_Control.connect_illuminometer(1, str(self.ui.spec_comboBox_serials.currentText()))
            self.ui.pushButton_spec_status.setStyleSheet("background-color:#00FF00")  #
            # print('ok')

        self.ui.spec_serials_connect.clicked.connect(connect)

    def init_spd_widget_list(self):
        str_ = "self.ui.widget_"
        for i in range(self.spd_num):
            i += 1
            self.spd_widget_list.append(eval(str_ + str(i)))

        # 存在一个总调控的widget
        self.spd_widget_list.append(eval(str_ + str(self.spd_num)))

        for i in range(self.spd_num + 1, 37):
            i += 1
            eval(str_ + str(i)).setVisible(False)

    def init_spd_checkBox_list(self):
        str_ = "self.ui.checkBox_"
        for i in range(self.spd_num + 1):
            i += 1
            self.spd_checkBox_list.append(eval(str_ + str(i)))

    def init_spd_slider_list(self):
        str_ = "self.ui.verticalSlider_"
        for i in range(self.spd_num + 1):
            i += 1
            self.spd_slider_list.append(eval(str_ + str(i)))

    def init_spd_lineEdit_list(self):
        str_ = "self.ui.lineEdit_"
        for i in range(self.spd_num + 1):
            i += 1
            self.spd_lineEdit_list.append(eval(str_ + str(i)))

    def init_spd_slider_change(self):
        # todo:发送数据接口,只要slider发生改变就发送数据
        global spd_slider_index
        for spd_slider_index in range(self.spd_num + 1):
            def sliderChange(n, value):
                if n == self.spd_num:
                    print("总调控单独控制")
                else:
                    # 获取所有通道的值
                    all_value = [x.value() / 1000 for x in self.spd_slider_list][:-1]
                    self.current_wave = np.sum(self.led_data_max_channel * all_value, 1)
                    if self.slider_draw == True:
                        # 避免蓝色线显示
                        self.graphUpdate(np.arange(751) + 350, self.current_wave, self.match_wave, self.measure_wave)
                        print('-----self.match_wave:-------', self.measure_wave)
                        pass
                self.spd_lineEdit_list[n].setText(str(value / 10))

            self.spd_slider_list[spd_slider_index].valueChanged.connect(
                lambda x, n=spd_slider_index: sliderChange(n, x))

            def sliderRelease(n):
                if self.current_protocol != None:
                    if n == self.spd_num:
                        print("总调控单独控制")
                    else:
                        # 刷新指令
                        self.current_protocol = getOrder(self.current_protocol, n + 1,
                                                         self.spd_slider_list[n].value() / 1000)
                        self.ui.textBrowser_message.append("\n[INFO] %s \n%d 通道发送数据: %d [0-1000]" % (
                            time.strftime("%Y-%m-%d %H:%M:%S", time.gmtime()), n + 1, self.spd_slider_list[n].value()))
                        self.ui.textBrowser_message.append("\n[SEND] %s \n%s" % (
                            time.strftime("%Y-%m-%d %H:%M:%S", time.gmtime()), self.current_protocol))
                        self.serial.write(bytes.fromhex(self.current_protocol))
                        print('self.current_protocol:', self.current_protocol)
                        self.ui.textBrowser_message.moveCursor(self.ui.textBrowser_message.textCursor().End)

            self.spd_slider_list[spd_slider_index].sliderReleased.connect(lambda n=spd_slider_index: sliderRelease(n))

    def init_spd_lineEdit_press(self):
        # 触发slider改变，与发送数据
        global spd_lineEdit_index
        for spd_lineEdit_index in range(self.spd_num + 1):
            def lineEditPress(n):
                value = eval(self.spd_lineEdit_list[n].text())
                value = round(value, 1)
                self.spd_lineEdit_list[n].setText(str(value))
                value = min(value * 10, 1000)

                if n == self.spd_num:
                    print("总调控单独控制-press")
                else:
                    # 刷新指令
                    try:
                        self.current_protocol = getOrder(self.current_protocol, n + 1, value / 1000)
                        self.ui.textBrowser_message.append("\n[INFO] %s \n%d 通道发送数据: %d [0-1000]" % (
                            time.strftime("%Y-%m-%d %H:%M:%S", time.gmtime()), n + 1, value))
                        self.ui.textBrowser_message.append("\n[SEND] %s \n%s" % (
                            time.strftime("%Y-%m-%d %H:%M:%S", time.gmtime()), self.current_protocol))
                        self.ui.textBrowser_message.moveCursor(self.ui.textBrowser_message.textCursor().End)
                        self.serial.write(bytes.fromhex(self.current_protocol))
                    except:
                        self.ui.textBrowser_message.append("\n[WARING] %s \n没有选择设备，暂无法发送数据!" % (
                            time.strftime("%Y-%m-%d %H:%M:%S", time.gmtime())))
                        self.ui.textBrowser_message.moveCursor(self.ui.textBrowser_message.textCursor().End)
                    finally:
                        self.spd_slider_list[n].setValue(value)

            self.spd_lineEdit_list[spd_lineEdit_index].returnPressed.connect(
                lambda n=spd_lineEdit_index: lineEditPress(n))
            # print('-----self.spd_lineEdit_list:---------',self.spd_lineEdit_list)

    def init_spd_checkBox_enable(self):
        global spd_checkBox_index
        for spd_checkBox_index in range(self.spd_num + 1):
            def checked(n, x):
                if x == 0:
                    # 失活
                    self.spd_slider_list[n].setEnabled(False)
                    self.spd_lineEdit_list[n].setEnabled(False)
                    # 置零
                    self.spd_slider_list[n].setValue(0)
                    self.spd_lineEdit_list[n].setText("0.0")
                    # 发送数据
                    if n == self.spd_num:
                        print("发送数据全0")
                    else:
                        print("%d 通道发送数据: %.1f" % (n + 1, self.spd_slider_list[n].value() / 10))
                if x == 2:
                    self.spd_slider_list[n].setEnabled(True)
                    self.spd_lineEdit_list[n].setEnabled(True)

            self.spd_checkBox_list[spd_checkBox_index].stateChanged.connect(
                lambda x, n=spd_checkBox_index: checked(n, x))

    def watch_serial_port(self):
        print('read com')
        # 观测串口
        # def watch():
        # while(True):
        # 初始化串Q口连接显示
        port_list = list(list_ports.comports())
        port_list_name = []
        for each_port in port_list:
            port_list_name.append(each_port[0])

        # 清零重新添加
        self.ui.comboBox_serials.clear()
        self.ui.spec_comboBox_serials.clear()
        self.ui.comboBox_serials_Le007.clear()
        self.ui.comboBox_serials_2.clear()
        self.ui.comboBox_serials_3.clear()
        self.ui.comboBox_serials_4.clear()
        self.ui.comboBox_serials_5.clear()
        self.ui.comboBox_serials_6.clear()

        for name in port_list_name:
            self.ui.comboBox_serials.addItem(name)
            self.ui.spec_comboBox_serials.addItem(name)
            self.ui.comboBox_cs_lm01.addItem(name)
            self.ui.comboBox_serials_Le007.addItem(name)
            self.ui.comboBox_serials_2.addItem(name)
            self.ui.comboBox_serials_3.addItem(name)
            self.ui.comboBox_serials_4.addItem(name)
            self.ui.comboBox_serials_5.addItem(name)
            self.ui.comboBox_serials_6.addItem(name)
            # 如果减少
            # if len(port_list_name) < len(self.serials_names):
            #     serials_reduce = set(self.serials_names) - set(port_list_name)
            #     if self.current_serial in list(serials_reduce):
            #         self.signal.error.emit("错误!","当前连接端口：%s \n已拔出!"%self.current_serial)
            #         self.ui.textBrowser_message.append("[ERROR]串口: %s 失去连接"%self.current_serial)
            #         self.ui.textBrowser_message.moveCursor(self.ui.textBrowser_message.textCursor().End)
            #         self.ui.pushButton_serials_connect.setText("连接")
            #         self.ui.comboBox_serials.setCurrentIndex(-1)
            #         self.serial.close()

            # self.serials_names = port_list_name
            # self.ui.comboBox_serials.update()
            # time.sleep(0.3)
        # watch_thread = Thread(target=watch)
        # watch_thread.start()

    def init_btn_connect(self):
        def connect():
            if self.ui.pushButton_serials_connect.text() == "连接":
                try:
                    # 闪耀现实波特率9600 普通版本115200
                    self.serial = serial.Serial(self.ui.comboBox_serials.currentText(), 115200, bytesize=8, parity='N',
                                                stopbits=1, timeout=0.1)
                    self.current_protocol = self.protocol[self.ui.comboBox_protocol.currentText()]
                    self.ui.pushButton_serials_connect.setText("断开")
                    self.ui.textBrowser_message.append("\n[INFO] %s \n连接光源1串口: %s" % (
                        time.strftime("%Y-%m-%d %H:%M:%S", time.gmtime()), self.ui.comboBox_serials.currentText()))
                    self.ui.textBrowser_message.moveCursor(self.ui.textBrowser_message.textCursor().End)
                    print('连接成功')
                    self.ui.pushButton_light_status.setStyleSheet("background-color:#00FF00")  #
                except:
                    self.signal.error.emit("错误!", "%s 连接失败!" % self.ui.comboBox_serials.currentText())
                    self.ui.textBrowser_message.append("\n[ERROR] %s \n连接光源1串口: %s 失败" % (
                        time.strftime("%Y-%m-%d %H:%M:%S", time.gmtime()), self.ui.comboBox_serials.currentText()))
                    self.ui.textBrowser_message.moveCursor(self.ui.textBrowser_message.textCursor().End)
                    print('连接失败')
                    self.ui.pushButton_light_status.setStyleSheet("background-color:#FF0000")  #

                try:
                    # 闪耀现实波特率9600 普通版本115200
                    self.serial_2 = serial.Serial(self.ui.comboBox_serials_2.currentText(), 115200, bytesize=8,
                                                  parity='N',
                                                  stopbits=1, timeout=0.1)
                    # self.current_protocol = self.protocol[self.ui.comboBox_protocol.currentText()]
                    self.ui.pushButton_serials_connect.setText("断开")
                    self.ui.textBrowser_message.append("\n[INFO] %s \n连接光源2串口: %s" % (
                        time.strftime("%Y-%m-%d %H:%M:%S", time.gmtime()), self.ui.comboBox_serials_2.currentText()))
                    self.ui.textBrowser_message.moveCursor(self.ui.textBrowser_message.textCursor().End)
                    print('连接成功')
                    self.ui.pushButton_light_status.setStyleSheet("background-color:#00FF00")  #
                except:
                    self.signal.error.emit("错误!", "%s 连接光源2失败!" % self.ui.comboBox_serials_2.currentText())
                    self.ui.textBrowser_message.append("\n[ERROR] %s \n连接光源2串口: %s 失败" % (
                        time.strftime("%Y-%m-%d %H:%M:%S", time.gmtime()), self.ui.comboBox_serials_2.currentText()))
                    self.ui.textBrowser_message.moveCursor(self.ui.textBrowser_message.textCursor().End)
                    print('连接失败')
                    self.ui.pushButton_light_status.setStyleSheet("background-color:#FF0000")  #

                try:
                    # 闪耀现实波特率9600 普通版本115200
                    self.serial_3 = serial.Serial(self.ui.comboBox_serials_3.currentText(), 115200, bytesize=8,
                                                  parity='N',
                                                  stopbits=1, timeout=0.1)
                    # self.current_protocol = self.protocol[self.ui.comboBox_protocol.currentText()]
                    self.ui.pushButton_serials_connect.setText("断开")
                    self.ui.textBrowser_message.append("\n[INFO] %s \n连接光源3串口: %s" % (
                        time.strftime("%Y-%m-%d %H:%M:%S", time.gmtime()), self.ui.comboBox_serials_3.currentText()))
                    self.ui.textBrowser_message.moveCursor(self.ui.textBrowser_message.textCursor().End)
                    print('连接成功')
                    self.ui.pushButton_light_status.setStyleSheet("background-color:#00FF00")  #
                except:
                    self.signal.error.emit("错误!", "%s 连接光源3失败!" % self.ui.comboBox_serials_3.currentText())
                    self.ui.textBrowser_message.append("\n[ERROR] %s \n连接光源3串口: %s 失败" % (
                        time.strftime("%Y-%m-%d %H:%M:%S", time.gmtime()), self.ui.comboBox_serials_3.currentText()))
                    self.ui.textBrowser_message.moveCursor(self.ui.textBrowser_message.textCursor().End)
                    print('连接失败')
                    self.ui.pushButton_light_status.setStyleSheet("background-color:#FF0000")  #

                try:
                    # 闪耀现实波特率9600 普通版本115200
                    self.serial_4 = serial.Serial(self.ui.comboBox_serials_4.currentText(), 115200, bytesize=8,
                                                  parity='N',
                                                  stopbits=1, timeout=0.1)
                    # self.current_protocol = self.protocol[self.ui.comboBox_protocol.currentText()]
                    self.ui.pushButton_serials_connect.setText("断开")
                    self.ui.textBrowser_message.append("\n[INFO] %s \n连接光源4串口: %s" % (
                        time.strftime("%Y-%m-%d %H:%M:%S", time.gmtime()), self.ui.comboBox_serials_4.currentText()))
                    self.ui.textBrowser_message.moveCursor(self.ui.textBrowser_message.textCursor().End)
                    print('连接成功')
                    self.ui.pushButton_light_status.setStyleSheet("background-color:#00FF00")  #
                except:
                    self.signal.error.emit("错误!", "%s 连接光源4失败!" % self.ui.comboBox_serials_4.currentText())
                    self.ui.textBrowser_message.append("\n[ERROR] %s \n连接光源4串口: %s 失败" % (
                        time.strftime("%Y-%m-%d %H:%M:%S", time.gmtime()), self.ui.comboBox_serials_4.currentText()))
                    self.ui.textBrowser_message.moveCursor(self.ui.textBrowser_message.textCursor().End)
                    print('连接失败')
                    self.ui.pushButton_light_status.setStyleSheet("background-color:#FF0000")  #

                try:
                    # 闪耀现实波特率9600 普通版本115200
                    self.serial_5 = serial.Serial(self.ui.comboBox_serials_5.currentText(), 115200, bytesize=8,
                                                  parity='N',
                                                  stopbits=1, timeout=0.1)
                    # self.current_protocol = self.protocol[self.ui.comboBox_protocol.currentText()]
                    self.ui.pushButton_serials_connect.setText("断开")
                    self.ui.textBrowser_message.append("\n[INFO] %s \n连接光源5串口: %s" % (
                        time.strftime("%Y-%m-%d %H:%M:%S", time.gmtime()), self.ui.comboBox_serials_5.currentText()))
                    self.ui.textBrowser_message.moveCursor(self.ui.textBrowser_message.textCursor().End)
                    print('连接成功')
                    self.ui.pushButton_light_status.setStyleSheet("background-color:#00FF00")  #
                except:
                    self.signal.error.emit("错误!", "%s 连接光源2失败!" % self.ui.comboBox_serials_5.currentText())
                    self.ui.textBrowser_message.append("\n[ERROR] %s \n连接光源5串口: %s 失败" % (
                        time.strftime("%Y-%m-%d %H:%M:%S", time.gmtime()), self.ui.comboBox_serials_5.currentText()))
                    self.ui.textBrowser_message.moveCursor(self.ui.textBrowser_message.textCursor().End)
                    print('连接失败')
                    self.ui.pushButton_light_status.setStyleSheet("background-color:#FF0000")  #

                try:
                    # 闪耀现实波特率9600 普通版本115200
                    self.serial_6 = serial.Serial(self.ui.comboBox_serials_6.currentText(), 115200, bytesize=8,
                                                  parity='N',
                                                  stopbits=1, timeout=0.1)
                    # self.current_protocol = self.protocol[self.ui.comboBox_protocol.currentText()]
                    self.ui.pushButton_serials_connect.setText("断开")
                    self.ui.textBrowser_message.append("\n[INFO] %s \n连接光源6串口: %s" % (
                        time.strftime("%Y-%m-%d %H:%M:%S", time.gmtime()), self.ui.comboBox_serials_6.currentText()))
                    self.ui.textBrowser_message.moveCursor(self.ui.textBrowser_message.textCursor().End)
                    print('连接成功')
                    self.ui.pushButton_light_status.setStyleSheet("background-color:#00FF00")  #
                except:
                    self.signal.error.emit("错误!", "%s 连接光源2失败!" % self.ui.comboBox_serials_6.currentText())
                    self.ui.textBrowser_message.append("\n[ERROR] %s \n连接光源6串口: %s 失败" % (
                        time.strftime("%Y-%m-%d %H:%M:%S", time.gmtime()), self.ui.comboBox_serials_6.currentText()))
                    self.ui.textBrowser_message.moveCursor(self.ui.textBrowser_message.textCursor().End)
                    print('连接失败')
                    self.ui.pushButton_light_status.setStyleSheet("background-color:#FF0000")  #

            elif self.ui.pushButton_serials_connect.text() == "断开":
                try:
                    self.serial.close()
                    self.current_protocol = None
                    self.ui.pushButton_serials_connect.setText("断开")
                    self.ui.textBrowser_message.append("\n[INFO] %s \n断开串口: %s" % (
                        time.strftime("%Y-%m-%d %H:%M:%S", time.gmtime()), self.ui.comboBox_serials.currentText()))
                    self.ui.textBrowser_message.moveCursor(self.ui.textBrowser_message.textCursor().End)
                    self.ui.pushButton_light_status.setStyleSheet("background-color:#FF0000")  #
                    print('断开成功')
                except:
                    self.ui.textBrowser_message.append("\n[ERROR] %s \n断开串口: %s 失败" % (
                        time.strftime("%Y-%m-%d %H:%M:%S", time.gmtime()), self.ui.comboBox_serials.currentText()))
                    self.ui.textBrowser_message.moveCursor(self.ui.textBrowser_message.textCursor().End)
                    self.ui.pushButton_light_status.setStyleSheet("background-color:#FF0000")  #
                    print('断开失败')

                self.ui.pushButton_serials_connect.setText("连接")

        self.ui.pushButton_serials_connect.clicked.connect(connect)

        # 反射光源连接
        def connect_le007():
            if self.ui.pushButton_le007_connect.text() == "连接":
                try:
                    # 闪耀现实波特率9600 普通版本115200
                    # self.serial = serial.Serial(self.ui.comboBox_serials.currentText(), 115200, bytesize=8, parity='N', stopbits=1, timeout=0.1)
                    # self.current_protocol = self.protocol[self.ui.comboBox_protocol.currentText()]
                    # light_api.connect(self.ui.comboBox_serials_Le007.currentText())
                    self.dmx.connect_dmx(self.ui.comboBox_serials_Le007.currentText())
                    # self.ui.pushButton_le007_left_switch.setEnabled(True)
                    # self.ui.pushButton_le007_right_switch.setEnabled(True)
                    self.ui.pushButton_le007_left_send.setEnabled(True)
                    # self.ui.pushButton_le007_right_send.setEnabled(True)
                    # self.ui.pushButton_le007_all_switch.setEnabled(True)
                    # self.ui.pushButton_le007_all_send.setEnabled(True)

                    self.ui.pushButton_le007_connect.setText("断开")
                    self.ui.textBrowser_message.append("\n[INFO] %s \n连接串口: %s" % (
                        time.strftime("%Y-%m-%d %H:%M:%S", time.gmtime()),
                        self.ui.comboBox_serials_Le007.currentText()))
                    self.ui.textBrowser_message.moveCursor(self.ui.textBrowser_message.textCursor().End)
                    print('连接成功')
                    self.ui.pushButton_le007_status.setStyleSheet("background-color:#00FF00")  #
                except:
                    self.signal.error.emit("错误!", "%s 连接失败!" % self.ui.comboBox_serials_Le007.currentText())
                    self.ui.textBrowser_message.append("\n[ERROR] %s \n连接串口: %s 失败" % (
                        time.strftime("%Y-%m-%d %H:%M:%S", time.gmtime()),
                        self.ui.comboBox_serials_Le007.currentText()))
                    self.ui.textBrowser_message.moveCursor(self.ui.textBrowser_message.textCursor().End)
                    print('连接失败')
                    self.ui.pushButton_le007_status.setStyleSheet("background-color:#FF0000")  #

            elif self.ui.pushButton_le007_connect.text() == "断开":
                try:
                    #   self.serial.close()
                    #   self.current_protocol = None
                    light_api.disconnect()
                    self.ui.pushButton_le007_connect.setText("断开")
                    self.ui.textBrowser_message.append("\n[INFO] %s \n断开串口: %s" % (
                        time.strftime("%Y-%m-%d %H:%M:%S", time.gmtime()), self.ui.comboBox_serials.currentText()))
                    self.ui.textBrowser_message.moveCursor(self.ui.textBrowser_message.textCursor().End)
                    self.ui.pushButton_le007_status.setStyleSheet("background-color:#FF0000")  #
                    print('断开成功')
                except:
                    self.ui.textBrowser_message.append("\n[ERROR] %s \n断开串口: %s 失败" % (
                        time.strftime("%Y-%m-%d %H:%M:%S", time.gmtime()), self.ui.comboBox_serials.currentText()))
                    self.ui.textBrowser_message.moveCursor(self.ui.textBrowser_message.textCursor().End)
                    self.ui.pushButton_le007_status.setStyleSheet("background-color:#FF0000")  #
                    print('断开失败')

                self.ui.pushButton_le007_connect.setText("连接")

        self.ui.pushButton_le007_connect.clicked.connect(connect_le007)

        # 反射光源按钮控制 pushButton_le007_left_switch
        # def pushButton_le007_left_switch_click():
        #     if self.ui.pushButton_le007_left_switch.text() == "ON":
        #         try:
        #             light_api.multi_controls(adrs=["01"], options=[["打开"]])
        #             self.ui.pushButton_le007_left_switch.setText("OFF")
        #             self.ui.pushButton_le007_left_switch.setStyleSheet(
        #                 'QPushButton{border-image: url(./ui/switch_new_on.png)}')
        #         except:
        #             pass
        #     elif self.ui.pushButton_le007_left_switch.text() == "OFF":
        #         try:
        #             light_api.multi_controls(adrs=["01"], options=[["关闭"]])
        #             self.ui.pushButton_le007_left_switch.setText("ON")
        #             self.ui.pushButton_le007_left_switch.setStyleSheet(
        #                 'QPushButton{border-image: url(./ui/switch_new_off.png)}')
        #
        #         except:
        #             pass

        # self.ui.pushButton_le007_left_switch.clicked.connect(pushButton_le007_left_switch_click)

        # # 右侧光源控制
        # def pushButton_le007_right_switch_click():
        #     if self.ui.pushButton_le007_right_switch.text() == "ON":
        #         try:
        #             light_api.multi_controls(adrs=["02"], options=[["打开"]])
        #             self.ui.pushButton_le007_right_switch.setStyleSheet(
        #                 'QPushButton{border-image: url(./ui/switch_new_on.png)}')
        #
        #             self.ui.pushButton_le007_right_switch.setText("OFF")
        #         except:
        #             pass
        #     elif self.ui.pushButton_le007_right_switch.text() == "OFF":
        #         try:
        #             light_api.multi_controls(adrs=["02"], options=[["关闭"]])
        #             self.ui.pushButton_le007_right_switch.setStyleSheet(
        #                 'QPushButton{border-image: url(./ui/switch_new_off.png)}')
        #
        #             self.ui.pushButton_le007_right_switch.setText("ON")
        #         except:
        #             pass

        # self.ui.pushButton_le007_right_switch.clicked.connect(pushButton_le007_right_switch_click)

        # 全部打开 LE007
        # def pushButton_le007_all_switch_click():
        #     if self.ui.pushButton_le007_all_switch.text() == "ON":
        #         try:
        #             light_api.multi_controls(adrs=["01", "02"], options=[["打开"], ["打开"]])
        #             self.ui.pushButton_le007_all_switch.setText("OFF")
        #             self.ui.pushButton_le007_left_switch.setStyleSheet(
        #                 'QPushButton{border-image: url(./ui/switch_new_on.png)}')
        #             self.ui.pushButton_le007_right_switch.setStyleSheet(
        #                 'QPushButton{border-image: url(./ui/switch_new_on.png)}')
        #
        #         except:
        #             pass
        #     elif self.ui.pushButton_le007_all_switch.text() == "OFF":
        #         try:
        #             light_api.multi_controls(adrs=["01", "02"], options=[["关闭"], ["关闭"]])
        #             self.ui.pushButton_le007_all_switch.setText("ON")
        #             self.ui.pushButton_le007_left_switch.setStyleSheet(
        #                 'QPushButton{border-image: url(./ui/switch_new_off.png)}')
        #             self.ui.pushButton_le007_right_switch.setStyleSheet(
        #                 'QPushButton{border-image: url(./ui/switch_new_off.png)}')
        #         except:
        #             pass

        # self.ui.pushButton_le007_all_switch.clicked.connect(pushButton_le007_all_switch_click)

        # LE007 单独左侧设置
        def pushButton_le007_left_send_click():
            # if self.ui.pushButton_le007_left_send.text() == "ON":
            try:
                ill = self.ui.spinBox_le007_left_illum.value()
                self.dmx.set_data(1, ill + 100)
                # print()
                print('light control:', ill + 100)

            # cct = self.ui.spinBox_le007_left_cct.value()
            # print('ill,cct:', ill, cct)
            # light_api.multi_controls(adrs=["01"], options=[["可见光(修改当前)", (cct, ill)]])
            except:
                pass

        self.ui.pushButton_le007_left_send.clicked.connect(pushButton_le007_left_send_click)

        # LE007 单独右侧设置
        def pushButton_le007_right_send_click():
            # if self.ui.pushButton_le007_right_send.text() == "ON":
            try:
                ill = self.ui.spinBox_le007_right_illum.value()
                cct = self.ui.spinBox_le007_right_cct.value()
                print('ill,cct:', ill, cct)
                light_api.multi_controls(adrs=["02"], options=[["可见光(修改当前)", (cct, ill)]])
            except:
                pass

        # self.ui.pushButton_le007_right_send.clicked.connect(pushButton_le007_right_send_click)

        # 全部发送
        def pushButton_le007_all_send_click():
            # if self.ui.pushButton_le007_right_send.text() == "ON":
            try:
                ill = self.ui.spinBox_le007_all_illum.value()
                cct = self.ui.spinBox_le007_all_cct.value()
                print('ill,cct:', ill, cct)
                light_api.multi_controls(adrs=["01", "02"],
                                         options=[["可见光(修改当前)", (cct, ill)], ["可见光(修改当前)", (cct, ill)]])
            except:
                pass

        # self.ui.pushButton_le007_all_send.clicked.connect(pushButton_le007_all_send_click)

        def cs_lm01_connect():
            if self.ui.measure_comboBox.currentText() == 'CS-LM01':
                Spectrometer_Control.connect(self.ui.comboBox_cs_lm01.currentText(), 'CS-LM01')
                self.ui.pushButton_cs_lm01_status.setStyleSheet("background-color:#00FF00")
            elif self.ui.measure_comboBox.currentText() == 'CS-LM01IR':
                print('ok')
                Spectrometer_Control_IR.connect(self.ui.comboBox_cs_lm01.currentText(), 'CS-LM01IR')
                self.ui.pushButton_cs_lm01_status.setStyleSheet("background-color:#00FF00")

        self.ui.pushButton_cs_lm01.clicked.connect(cs_lm01_connect)

        def show_led_data():
            # 显示峰值亮度与线性化程度
            led_datas = []
            led_linear_x = []
            led_linear_y = []
            for led_data in self.led_data_arrays:
                led_datas.append(led_data[1:, 0])
                led_linear_x.append(led_data[0, :])
                led_linear_y.append(np.max(led_data[1:, ], 0))

            led_datas = np.reshape(led_datas, (self.spd_num, -1))
            x = np.linspace(350, 1100, 751)
            fig1 = plt.figure("Multi-channel energy value", figsize=(7, 7 * 0.618))
            ax1 = fig1.add_subplot(111)
            ax1.patch.set_facecolor('#FFFFFF')
            fig1.patch.set_facecolor('#FFFFFF')

            fig2 = plt.figure("Multi-channel linear", figsize=(10, 10 * 0.618))
            ax2 = fig2.add_subplot(111)
            ax2.patch.set_facecolor('#FFFFFF')
            fig2.patch.set_facecolor('#FFFFFF')

            for data, linear_x, linear_y in zip(led_datas, led_linear_x, led_linear_y):
                peek = np.argmax(data) + 350
                rgb = wavelength_to_rgb(peek)
                html = RGBToHTMLColor(rgb)
                ax1.plot(x, data, color=html)
                ax1.text(peek, np.max(data), str(peek), color=html, fontsize=7, weight="black")
                ax2.plot(linear_x, linear_y, color=html, label=str(peek), marker="*")
                ax2.text(linear_x[0] + 10, linear_y[0], str(peek), color=html, fontsize=7, weight="black")
            ax2.set_xticks(led_linear_x[0])
            plt.xticks(fontsize=7, color='white', rotation=65)
            # plt.tick_params(axis='x',colors='white')
            plt.legend(loc=2, fontsize=7)
            # plt.subplots_adjust(left=0.05, right=0.95, top=0.95, bottom=0.05)
            plt.show()

        self.ui.pushButton_watch_led.clicked.connect(show_led_data)

        def spdImport():
            filePath, _ = QFileDialog.getOpenFileName(
                self.ui,  # 父窗口对象
                "选择光谱数据",  # 标题
                r"./",  # 起始目录
                "*.csv")
            self.ui.lineEdit_MatchSpdPath.setText(filePath)

            match_std_data = np.loadtxt(filePath, delimiter=",")
            LEDFunc = interp1d(np.linspace(350, 1100, np.size(match_std_data)), match_std_data, "linear")
            insertSTDData = LEDFunc(np.arange(350, 1101))
            insertSTDData = insertSTDData / np.max(insertSTDData)
            self.match_wave = insertSTDData
            self.graphUpdate(np.arange(751) + 350, self.current_wave, self.match_wave, self.measure_wave)

        self.ui.pushButton_MatchSpdImport.clicked.connect(spdImport)

        # 绘图
        # def pushButton_saveOrder_click(self):
        #     print('ok')

        # 测量目标数据
        def measureTargetSpd():
            print("测量按钮被点击")
            # 测试：直接启用校正按钮
            print("测试：直接启用校正按钮")
            self.correction_enabled = True
            self.ui.pushButton_correction.setEnabled(True)
            print("校正按钮应该已启用")

            def measureRun0():
                print("开始测量线程")
                # 至灰
                self.ui.pushButton_MatchSpdMeasure.setEnabled(False)
                try:
                    # 检测是否含有已存在文件
                    if os.path.exists("./Measurement.csv"):
                        os.remove("./Measurement.csv")
                    # 调用测量
                    # os.system("Measurement.exe")
                    if self.ui.measure_comboBox.currentText() == 'CL-500A':
                        os.system("Measurement.exe")
                    elif self.ui.measure_comboBox.currentText() == 'CS-LM01':
                        Spectrometer_Control.cms_conntrol(self.ui.comboBox_cs_lm01.currentText(), 'CS-LM01',
                                                          'Fiber.csv')
                    elif self.ui.measure_comboBox.currentText() == 'CS-LM01IR':
                        Spectrometer_Control_IR.cms_conntrol(self.ui.comboBox_cs_lm01.currentText(), 'CS-LM01IR',
                                                             'Fiber.csv')

                    time.sleep(1)
                    with open('Measurement.csv', 'r') as f:
                        reader = csv.reader(f)
                        for index, row in enumerate(reader):
                            # 记录展示光谱值
                            if index == 0:
                                Spc = [eval(x) for x in row[1:] if x != ""]
                                # 保存
                                if not os.path.exists("./spdTemp"):
                                    os.makedirs("./spdTemp")
                                timeRecord = time.strftime("%Y-%m-%d-%H:%M:%S", time.localtime())
                                savePath = "./spdTemp/" + timeRecord + ".csv"
                                savePath = savePath.replace("-", "_")
                                savePath = savePath.replace(":", "_")
                                self.measure_wave = Spc
                                print(f"测量数据已保存，长度: {len(self.measure_wave)}")
                                self.graphUpdate(np.linspace(350, 1100, 751), self.current_wave, self.match_wave,
                                                 self.measure_wave)
                                np.savetxt(savePath, Spc)
                                self.ui.lineEdit_MatchSpdPath.setText(savePath)
                    # 弹出测量成功
                    self.signal.note.emit('提示', 'CL500A测量成功!')
                    self.ui.textBrowser_message.append("\n[INFO] %s \nCL500A测量完毕!\n 保存路径：%s" % (
                        time.strftime("%Y-%m-%d %H:%M:%S", time.gmtime()), savePath))
                    self.ui.textBrowser_message.moveCursor(self.ui.textBrowser_message.textCursor().End)

                except Exception as e:
                    print(f"测量过程中发生异常: {e}")
                    # 检查500是否正常
                    # self.signal.error.emit("错误", "请检查500A是否正常!")
                    # self.ui.textBrowser_message.append("\n[ERROR] %s \nCL500A测量失败!请检查500A是否正常!"%(time.strftime("%Y-%m-%d %H:%M:%S",time.gmtime())))
                    # self.ui.textBrowser_message.moveCursor(self.ui.textBrowser_message.textCursor().End)

                # 无论测量是否成功，只要有测量数据就启用校正按钮
                if len(self.measure_wave) > 0:
                    # 使用信号在主线程中启用校正按钮
                    self.signal.enable_correction.emit()
                    # 检查500是否正常
                # self.signal.error.emit("错误", "请检查500A是否正常!")
                # self.ui.textBrowser_message.append("\n[ERROR] %s \nCL500A测量失败!请检查500A是否正常!"%(time.strftime("%Y-%m-%d %H:%M:%S",time.gmtime())))
                # self.ui.textBrowser_message.moveCursor(self.ui.textBrowser_message.textCursor().End)
                # 正常
                self.ui.pushButton_MatchSpdMeasure.setEnabled(True)

            mearsureThread0 = Thread(target=measureRun0)
            mearsureThread0.start()

        self.ui.pushButton_MatchSpdMeasure.clicked.connect(measureTargetSpd)

        # 测量多通道数据
        def measureChannels():
            def measureChannelsRun():
                # 获取测试节点
                channels = [eval(x) for x in self.ui.lineEdit_channels.text().split(",")]
                notes = [eval(x) for x in self.ui.lineEdit_notes.text().split(",")]
                self.ui.progressBar.setRange(0, len(channels) * len(notes))
                # 检查保存路径
                savePath = self.ui.lineEdit_correctionSavePath.text()
                timeRecord = time.strftime("%Y-%m-%d-%H:%M:%S", time.localtime())
                timeRecord = timeRecord.replace("-", "_")
                timeRecord = timeRecord.replace(":", "_")
                savePath = os.path.join(savePath, timeRecord)
                os.mkdir(savePath)

                self.ui.pushButton_startCorrection.setEnabled(False)
                # self.ui.textBrowser_message.append("\n[INFO] %s \n---------------------【CL500A测试】------------------------】"%(time.strftime("%Y-%m-%d %H:%M:%S",time.gmtime())))
                # self.ui.textBrowser_message.moveCursor(self.ui.textBrowser_message.textCursor().End)
                try:
                    # 检测是否含有已存在文件
                    if os.path.exists("./Measurement.csv"):
                        os.remove("./Measurement.csv")
                    time.sleep(0.5)
                    self.serial.write(
                        bytes.fromhex(getOrder(self.protocol[self.ui.comboBox_protocol.currentText()], 1, 1000 / 1000)))

                    time.sleep(3)

                    # 调用测量
                    if self.ui.measure_comboBox.currentText() == 'CL-500A':
                        os.system("Measurement.exe")
                    elif self.ui.measure_comboBox.currentText() == 'CS-LM01':
                        Spectrometer_Control.cms_conntrol(self.ui.comboBox_cs_lm01.currentText(), 'CS-LM01',
                                                          'Fiber.csv')
                    elif self.ui.measure_comboBox.currentText() == 'CS-LM01IR':
                        Spectrometer_Control_IR.cms_conntrol(self.ui.comboBox_cs_lm01.currentText(), 'CS-LM01IR',
                                                             'Fiber.csv')

                        # Spectrometer_Control_IR.cms_conntrol(self.ui.comboBox_cs_lm01.currentText(), 'CS-LM01IR',
                        #                                      'Fiber.csv')
                    time.sleep(1)

                    with open('Measurement.csv', 'r') as f:
                        reader = csv.reader(f)
                        for index, row in enumerate(reader):
                            # 记录展示光谱值
                            if index == 0:
                                Spc = [eval(x) for x in row[1:] if x != ""]
                                # 保存
                                if not os.path.exists("./spdTemp"):
                                    os.makedirs("./spdTemp")
                                timeRecord = time.strftime("%Y-%m-%d-%H:%M:%S", time.localtime())
                                timeRecord = timeRecord.replace("-", "_")
                                timeRecord = timeRecord.replace(":", "_")
                                savePath0 = "./spdTemp/" + timeRecord + ".csv"
                                np.savetxt(savePath0, Spc)
                                # self.ui.lineEdit_MatchSpdPath.setText(savePath0)
                except Exception as err:
                    # 检查500是否正常
                    self.signal.error.emit("错误", "请检查500A是否正常！")
                    self.ui.textBrowser_message.append(
                        "\n[ERROR] %s \n【---------------------【采集失败】------------------------】\n %s" % (
                            time.strftime("%Y-%m-%d %H:%M:%S", time.gmtime()), err))
                    self.ui.textBrowser_message.moveCursor(self.ui.textBrowser_message.textCursor().End)
                    self.ui.pushButton_startCorrection.setEnabled(True)
                    return

                # self.ui.textBrowser_message.append("\n[INFO] %s \n---------------------【CL500A测试正常】------------------------】"%(time.strftime("%Y-%m-%d %H:%M:%S",time.gmtime())))
                # self.ui.textBrowser_message.append("\n[INFO] %s \n---------------------【开始采集】------------------------】"%(time.strftime("%Y-%m-%d %H:%M:%S",time.gmtime())))
                # self.ui.textBrowser_message.moveCursor(self.ui.textBrowser_message.textCursor().End)
                try:
                    for channel in channels:
                        oneSpdList = []
                        for index, note in enumerate(notes):
                            if os.path.exists("./Measurement.csv"):
                                os.remove("./Measurement.csv")

                            # 这里要发送数据
                            self.current_protocol = getOrder(self.protocol[self.ui.comboBox_protocol.currentText()],
                                                             channel, note / 1000)
                            self.serial.write(bytes.fromhex(self.current_protocol))
                            time.sleep(2)
                            # self.ui.textBrowser_message.append("\n[SEND] %s \n%s" % (time.strftime("%Y-%m-%d %H:%M:%S",time.gmtime()),self.current_protocol))
                            # self.ui.textBrowser_message.append("\n[INFO] %s \n【测量】Channel:%d,Note:%d" % (time.strftime("%Y-%m-%d %H:%M:%S",time.gmtime()),channel, note))
                            # self.ui.textBrowser_message.moveCursor(self.ui.textBrowser_message.textCursor().End)
                            # 发送之后测量
                            # measure_flag = os.system("Measurement.exe")
                            if self.ui.measure_comboBox.currentText() == 'CL-500A':
                                os.system("Measurement.exe")
                            elif self.ui.measure_comboBox.currentText() == 'CS-LM01':
                                Spectrometer_Control.cms_conntrol(self.ui.comboBox_cs_lm01.currentText(), 'CS-LM01',
                                                                  'Fiber.csv')
                            elif self.ui.measure_comboBox.currentText() == 'CS-LM01IR':
                                Spectrometer_Control_IR.cms_conntrol(self.ui.comboBox_cs_lm01.currentText(),
                                                                     'CS-LM01IR',
                                                                     'Fiber.csv')
                            time.sleep(0.5)
                            # 测量完成后读取csv
                            measure_dict = {}
                            with open('Measurement.csv', 'r') as f:
                                reader = csv.reader(f)
                                for row in reader:
                                    measure_dict[row[0]] = [eval(x) for x in row[1:] if x != ""]
                            # 界面显示

                            if self.ui.measure_comboBox.currentText() == 'CS-LM01IR':
                                spd = measure_dict["Spc"]
                                oneSpdList.append(note)
                                # oneSpdList.extend(spd[10:431])
                                # 全部数据写入
                                oneSpdList.extend(spd)
                            else:
                                spd = measure_dict["Spc"]
                                oneSpdList.append(note)
                                oneSpdList.extend(spd)

                            self.ui.progressBar.setValue((channel - 1) * len(notes) + index + 1)

                        oneSpdList = list(oneSpdList)
                        oneSpd = np.reshape(oneSpdList, (len(notes), -1)).T
                        savePath0 = os.path.join(savePath, str(channel).zfill(2) + ".csv")
                        np.savetxt(savePath0, oneSpd, delimiter=",")
                        self.ui.textBrowser_message.append("\n[INFO] %s \n【测量】Channel:%d 完毕,保存路径:%s" % (
                            time.strftime("%Y-%m-%d %H:%M:%S", time.gmtime()), channel, savePath0))
                        self.ui.textBrowser_message.moveCursor(self.ui.textBrowser_message.textCursor().End)

                    self.ui.textBrowser_message.append(
                        "\n[INFO] %s \n---------------------【采集完成】------------------------】\n保存位置:%s" % (
                            time.strftime("%Y-%m-%d %H:%M:%S", time.gmtime()), savePath))
                    self.ui.textBrowser_message.moveCursor(self.ui.textBrowser_message.textCursor().End)
                    self.signal.note.emit("采集数据", "采集完成！")

                except Exception as err:
                    self.ui.pushButton_startCorrection.setEnabled(True)
                    self.ui.textBrowser_message.append(
                        "\n[ERROR] %s \n【---------------------【采集失败】------------------------】\n %s" % (
                            time.strftime("%Y-%m-%d %H:%M:%S", time.gmtime()), err))
                    self.ui.textBrowser_message.moveCursor(self.ui.textBrowser_message.textCursor().End)

                    self.signal.error.emit("错误", "采集失败！\n请检查配置!")
                    return

                self.ui.pushButton_startCorrection.setEnabled(True)

            self.measureThread = Thread(target=measureChannelsRun)
            self.measureThread.start()

        self.ui.pushButton_startCorrection.clicked.connect(measureChannels)

        def stopMeasureChannels():
            try:
                self.stop_thread(self.measureThread)
                self.ui.textBrowser_message.append(
                    "\n[INFO] %s \n【---------------------【停止采集】------------------------】\n" % (
                        time.strftime("%Y-%m-%d %H:%M:%S", time.gmtime())))
                self.ui.textBrowser_message.moveCursor(self.ui.textBrowser_message.textCursor().End)
                self.ui.pushButton_startCorrection.setEnabled(True)
            except Exception as err:
                self.ui.textBrowser_message.append(
                    "\n[INFO] %s \n【---------------------【停止失败】------------------------】\n %s" % (
                        time.strftime("%Y-%m-%d %H:%M:%S", time.gmtime()), err))
                self.ui.textBrowser_message.moveCursor(self.ui.textBrowser_message.textCursor().End)

        self.ui.pushButton_stopCorrection.clicked.connect(stopMeasureChannels)
        # 获取照度信息
        def measureCL500A():
            print(self.ui.measure_comboBox.currentText())
            # 500A测量 多线程修改没有
            # def measureCL500ARun():
            if self.ui.measure_comboBox.currentText() == 'CL-500A':
                try:
                    self.ui.textBrowser_message.append(
                        "\n[INFO] %s \n【---------------------【CL500A开始测量】------------------------】\n" % (
                            time.strftime("%Y-%m-%d %H:%M:%S", time.gmtime())))
                    self.ui.pushButton_measure500A.setEnabled(False)

                    measure_flag = os.system("Measurement.exe")

                    # 测量完成后读取csv
                    time.sleep(1)

                    self.measure_dict = {}
                    with open('Measurement.csv', 'r') as f:
                        reader = csv.reader(f)
                        for index, row in enumerate(reader):
                            self.measure_dict[row[0]] = [eval(x) for x in row[1:] if x != ""]
                            # 记录展示光谱值
                            if index == 0:
                                Spc = [eval(x) for x in row[1:] if x != ""]
                                # 保存
                                if not os.path.exists("./spdTemp"):
                                    os.makedirs("./spdTemp")
                                timeRecord = time.strftime("%Y-%m-%d-%H:%M:%S", time.localtime())
                                timeRecord = timeRecord.replace("-", "_")
                                timeRecord = timeRecord.replace(":", "_")
                                savePath0 = "./spdTemp/" + timeRecord + ".csv"
                                np.savetxt(savePath0, Spc)

                    # 界面显示
                    self.measure_wave = self.measure_dict["Spc"]
                    print('--------self.measure_dict["Spc"]:-------', self.measure_dict["Spc"],
                          len(self.measure_dict["Spc"]))
                    self.graphUpdate(np.linspace(350, 1100, 751), self.current_wave, self.match_wave, self.measure_wave)

                    self.ui.lineEdit_Measure_Ev.setText(str(self.measure_dict["Ev"][0]))
                    self.ui.lineEdit_Measure_x.setText(str(self.measure_dict["x"][0]))
                    self.ui.lineEdit_Measure_y.setText(str(self.measure_dict["y"][0]))
                    self.ui.lineEdit_Measure_X.setText(str(self.measure_dict["X"][0]))
                    self.ui.lineEdit_Measure_Y.setText(str(self.measure_dict["Y"][0]))
                    self.ui.lineEdit_Measure_Z.setText(str(self.measure_dict["Z"][0]))
                    self.ui.lineEdit_Measure_T.setText(str(self.measure_dict["T"][0]))
                    self.ui.lineEdit_Measure_duv.setText(str(self.measure_dict["duv"][0]))
                    self.ui.lineEdit_Measure_PW.setText(str(self.measure_dict["PW"][0]))
                    self.ui.lineEdit_Measure_DW.setText(str(self.measure_dict["DW"][0]))

                    # 置为Ra
                    self.ui.comboBox_Measure_Re.setCurrentIndex(0)

                    self.ui.lineEdit_Measure_Re.setText(str(int(float(self.measure_dict["Re"][0]))))
                    self.ui.lineEdit_Measure_Pe.setText(str(self.measure_dict["Pe"][0]))
                    self.ui.lineEdit_Measure_Es.setText(str(self.measure_dict["Es"][0]))
                    self.ui.lineEdit_Measure_SP.setText(str(self.measure_dict["SP"][0]))
                    self.ui.lineEdit_Measure_u.setText(str(self.measure_dict["u"][0]))
                    self.ui.lineEdit_Measure_v.setText(str(self.measure_dict["v"][0]))
                    print('-------------:', str(int(float(self.measure_dict["Re"][1]))))
                    ra1_ra5 = 'R1:' + str(int(float(self.measure_dict["Re"][1]))) + ',R2:' + str(
                        int(float(self.measure_dict["Re"][2]))) + ',R3:' + str(int(float(self.measure_dict["Re"][3]))) \
                              + ',R4:' + str(int(float(self.measure_dict["Re"][4]))) + ',R5:' + str(
                        int(float(self.measure_dict["Re"][5])))
                    ra6_ra10 = 'R6:' + str(int(float(self.measure_dict["Re"][6]))) \
                               + ',R7:' + str(int(float(self.measure_dict["Re"][7]))) + ',R8:' + str(
                        int(float(self.measure_dict["Re"][8]))) + ',R9:' + str(int(float(self.measure_dict["Re"][9]))) \
                               + ',R10:' + str(int(float(self.measure_dict["Re"][10])))
                    ra11_ra15 = 'R11:' + str(int(float(self.measure_dict["Re"][11]))) + ',R12:' + str(
                        int(float(self.measure_dict["Re"][12]))) \
                                + ',R13:' + str(int(float(self.measure_dict["Re"][13]))) + ',R14:' + str(
                        int(float(self.measure_dict["Re"][14]))) + ',R15:' + str(
                        int(float(self.measure_dict["Re"][15])))

                    self.ui.lineEdit_Measure_Re_1_to_5.setText(str(ra1_ra5))
                    self.ui.lineEdit_Measure_Re_6_to_10.setText(str(ra6_ra10))
                    self.ui.lineEdit_Measure_Re_11_to_15.setText(str(ra11_ra15))

                    self.ui.textBrowser_message.append(
                        "\n[INFO] %s \n【---------------------【CL500A测量结束】------------------------】\n" % (
                            time.strftime("%Y-%m-%d %H:%M:%S", time.gmtime())))
                except Exception as err:
                    self.ui.textBrowser_message.append(
                        "\n[INFO] %s \n【---------------------【CL500A测量失败】------------------------】\n%s" % (
                            time.strftime("%Y-%m-%d %H:%M:%S", time.gmtime()), err))
                    self.signal.error.emit("错误", "500A环境异常！")
                finally:
                    self.ui.pushButton_measure500A.setEnabled(True)

            #获取照度信息

            elif self.ui.measure_comboBox.currentText() == "CS-LM01IR":
                print('-------CS-LM01IR-------')
                # measure_flag = os.system("Measurement.exe")
                # 换成cs-lm01
                try:
                    self.ui.textBrowser_message.append(
                        "\n[INFO] %s \n【---------------------【CS-LM01IR开始测量】------------------------】\n" % (
                            time.strftime("%Y-%m-%d %H:%M:%S", time.gmtime())))
                    self.ui.pushButton_measure500A.setEnabled(False)
                    print('-----------:', self.ui.comboBox_cs_lm01.currentText())
                    Spectrometer_Control_IR.cms_conntrol(self.ui.comboBox_cs_lm01.currentText(), 'CS-LM01IR',
                                                         'Fiber.csv')
                    # Spectrometer_Control_IR.cms_conntrol(self.ui.comboBox_cs_lm01.currentText(), 'CS-LM01IR', 'Fiber.csv')
                    # 测量完成后读取csv
                    time.sleep(1)

                    self.measure_dict = {}
                    with open('Measurement.csv', 'r') as f:
                        reader = csv.reader(f)
                        for index, row in enumerate(reader):
                            self.measure_dict[row[0]] = [eval(x) for x in row[1:] if x != ""]
                            # 记录展示光谱值
                            if index == 0:
                                Spc = [eval(x) for x in row[1:] if x != ""]
                                # 保存
                                if not os.path.exists("./spdTemp"):
                                    os.makedirs("./spdTemp")
                                timeRecord = time.strftime("%Y-%m-%d-%H:%M:%S", time.localtime())
                                timeRecord = timeRecord.replace("-", "_")
                                timeRecord = timeRecord.replace(":", "_")
                                savePath0 = "./spdTemp/" + timeRecord + ".csv"
                                np.savetxt(savePath0, Spc)
                    print('len:', len(Spc))

                    # 界面显示
                    self.measure_wave = self.measure_dict["Spc"]
                    print('--------self.measure_dict["Spc"]:-------', self.measure_dict["Spc"],
                          len(self.measure_dict["Spc"]))
                    # self.graphUpdate(np.linspace(360, 780, 421), self.current_wave, self.match_wave, self.measure_wave)
                    print(np.size(self.current_wave))
                    print(np.size(self.match_wave))
                    print(np.size(self.measure_wave))
                    self.graphUpdate(np.linspace(350, 1100, 751), self.current_wave, self.match_wave, self.measure_wave)

                    self.ui.lineEdit_Measure_Ev.setText(str(self.measure_dict["Ev"][0]))
                    self.ui.lineEdit_Measure_x.setText(str(self.measure_dict["x"][0]))
                    self.ui.lineEdit_Measure_y.setText(str(self.measure_dict["y"][0]))
                    self.ui.lineEdit_Measure_X.setText(str(self.measure_dict["X"][0]))
                    self.ui.lineEdit_Measure_Y.setText(str(self.measure_dict["Y"][0]))
                    self.ui.lineEdit_Measure_Z.setText(str(self.measure_dict["Z"][0]))
                    self.ui.lineEdit_Measure_T.setText(str(self.measure_dict["T"][0]))#色温值
                    self.ui.lineEdit_Measure_duv.setText(str(self.measure_dict["duv"][0]))
                    self.ui.lineEdit_Measure_PW.setText(str(self.measure_dict["PW"][0]))
                    self.ui.lineEdit_Measure_DW.setText(str(self.measure_dict["DW"][0]))
                    # 置为Ra
                    self.ui.comboBox_Measure_Re.setCurrentIndex(0)

                    self.ui.lineEdit_Measure_Re.setText(str((float(self.measure_dict["Re"][15]))))
                    self.ui.lineEdit_Measure_Pe.setText(str(self.measure_dict["Pe"][0]))
                    self.ui.lineEdit_Measure_Es.setText(str(self.measure_dict["Es"][0]))
                    self.ui.lineEdit_Measure_SP.setText(str(self.measure_dict["SP"][0]))
                    self.ui.lineEdit_Measure_u.setText(str(self.measure_dict["u"][0]))
                    self.ui.lineEdit_Measure_v.setText(str(self.measure_dict["v"][0]))
                    # print('-------------:',str(int(float(self.measure_dict["Re"][1]))))

                    print('---self.measure_dict["Re"]:', self.measure_dict["Re"])
                    ra1_ra5 = 'R1:' + str(int(float(self.measure_dict["Re"][0]))) + ',R2:' + str(
                        int(float(self.measure_dict["Re"][1]))) + ',R3:' + str(int(float(self.measure_dict["Re"][2]))) \
                              + ',R4:' + str(int(float(self.measure_dict["Re"][3]))) + ',R5:' + str(
                        int(float(self.measure_dict["Re"][4])))
                    ra6_ra10 = 'R6:' + str(int(float(self.measure_dict["Re"][5]))) \
                               + ',R7:' + str(int(float(self.measure_dict["Re"][6]))) + ',R8:' + str(
                        int(float(self.measure_dict["Re"][7]))) + ',R9:' + str(int(float(self.measure_dict["Re"][8]))) \
                               + ',R10:' + str(int(float(self.measure_dict["Re"][9])))
                    ra11_ra15 = 'R11:' + str(int(float(self.measure_dict["Re"][10]))) + ',R12:' + str(
                        int(float(self.measure_dict["Re"][11]))) \
                                + ',R13:' + str(int(float(self.measure_dict["Re"][12]))) + ',R14:' + str(
                        int(float(self.measure_dict["Re"][13]))) + ',R15:' + str(
                        int(float(self.measure_dict["Re"][14])))

                    self.ui.lineEdit_Measure_Re_1_to_5.setText(str(ra1_ra5))
                    self.ui.lineEdit_Measure_Re_6_to_10.setText(str(ra6_ra10))
                    self.ui.lineEdit_Measure_Re_11_to_15.setText(str(ra11_ra15))

                    self.ui.textBrowser_message.append(
                        "\n[INFO] %s \n【---------------------【CS-LM01IR测量结束】------------------------】\n" % (
                            time.strftime("%Y-%m-%d %H:%M:%S", time.gmtime())))

                    # 测量成功后启用校正按钮
                    if len(self.measure_wave) > 0:
                        self.signal.enable_correction.emit()
                        print("CS-LM01IR测量完成，启用校正按钮")

                    self.ui.pushButton_measure500A.setEnabled(True)
                except Exception as err:
                    self.ui.textBrowser_message.append(
                        "\n[INFO] %s \n【---------------------【CS-LM01IR测量失败】------------------------】\n%s" % (
                            time.strftime("%Y-%m-%d %H:%M:%S", time.gmtime()), err))
                    self.signal.error.emit("错误", "CS-LM01IR环境异常！")
                finally:
                    self.ui.pushButton_measure500A.setEnabled(True)

            elif self.ui.measure_comboBox.currentText() == "CS-LM01":
                print('-------CS-LM01-------')
                # measure_flag = os.system("Measurement.exe")
                # 换成cs-lm01
                try:
                    self.ui.textBrowser_message.append(
                        "\n[INFO] %s \n【---------------------【CS-LM01开始测量】------------------------】\n" % (
                            time.strftime("%Y-%m-%d %H:%M:%S", time.gmtime())))
                    self.ui.pushButton_measure500A.setEnabled(False)
                    print('-----------:', self.ui.comboBox_cs_lm01.currentText())
                    Spectrometer_Control.cms_conntrol(self.ui.comboBox_cs_lm01.currentText(), 'CS-LM01', 'Fiber.csv')
                    # 测量完成后读取csv
                    time.sleep(1)

                    self.measure_dict = {}
                    with open('Measurement.csv', 'r') as f:
                        reader = csv.reader(f)
                        for index, row in enumerate(reader):
                            self.measure_dict[row[0]] = [eval(x) for x in row[1:] if x != ""]
                            # 记录展示光谱值
                            if index == 0:
                                Spc = [eval(x) for x in row[1:] if x != ""]
                                # 保存
                                if not os.path.exists("./spdTemp"):
                                    os.makedirs("./spdTemp")
                                timeRecord = time.strftime("%Y-%m-%d-%H:%M:%S", time.localtime())
                                timeRecord = timeRecord.replace("-", "_")
                                timeRecord = timeRecord.replace(":", "_")
                                savePath0 = "./spdTemp/" + timeRecord + ".csv"
                                np.savetxt(savePath0, Spc)

                    # 界面显示
                    self.measure_wave = self.measure_dict["Spc"]
                    print('--------self.measure_dict["Spc"]:-------', self.measure_dict["Spc"],
                          len(self.measure_dict["Spc"]))
                    self.graphUpdate(np.linspace(350, 1100, 751), self.current_wave, self.match_wave, self.measure_wave)

                    self.ui.lineEdit_Measure_Ev.setText(str(self.measure_dict["Ev"][0]))
                    self.ui.lineEdit_Measure_x.setText(str(self.measure_dict["x"][0]))
                    self.ui.lineEdit_Measure_y.setText(str(self.measure_dict["y"][0]))
                    self.ui.lineEdit_Measure_X.setText(str(self.measure_dict["X"][0]))
                    self.ui.lineEdit_Measure_Y.setText(str(self.measure_dict["Y"][0]))
                    self.ui.lineEdit_Measure_Z.setText(str(self.measure_dict["Z"][0]))
                    self.ui.lineEdit_Measure_T.setText(str(self.measure_dict["T"][0]))
                    self.ui.lineEdit_Measure_duv.setText(str(self.measure_dict["duv"][0]))
                    self.ui.lineEdit_Measure_PW.setText(str(self.measure_dict["PW"][0]))
                    self.ui.lineEdit_Measure_DW.setText(str(self.measure_dict["DW"][0]))
                    # 置为Ra
                    self.ui.comboBox_Measure_Re.setCurrentIndex(0)

                    self.ui.lineEdit_Measure_Re.setText(str((float(self.measure_dict["Re"][15]))))
                    self.ui.lineEdit_Measure_Pe.setText(str(self.measure_dict["Pe"][0]))
                    self.ui.lineEdit_Measure_Es.setText(str(self.measure_dict["Es"][0]))
                    self.ui.lineEdit_Measure_SP.setText(str(self.measure_dict["SP"][0]))
                    self.ui.lineEdit_Measure_u.setText(str(self.measure_dict["u"][0]))
                    self.ui.lineEdit_Measure_v.setText(str(self.measure_dict["v"][0]))
                    # print('-------------:',str(int(float(self.measure_dict["Re"][1]))))

                    print('---self.measure_dict["Re"]:', self.measure_dict["Re"])

                    ra1_ra5 = 'R1:' + str(int(float(self.measure_dict["Re"][0]))) + ',R2:' + str(
                        int(float(self.measure_dict["Re"][1]))) + ',R3:' + str(int(float(self.measure_dict["Re"][2]))) \
                              + ',R4:' + str(int(float(self.measure_dict["Re"][3]))) + ',R5:' + str(
                        int(float(self.measure_dict["Re"][4])))
                    ra6_ra10 = 'R6:' + str(int(float(self.measure_dict["Re"][5]))) \
                               + ',R7:' + str(int(float(self.measure_dict["Re"][6]))) + ',R8:' + str(
                        int(float(self.measure_dict["Re"][7]))) + ',R9:' + str(int(float(self.measure_dict["Re"][8]))) \
                               + ',R10:' + str(int(float(self.measure_dict["Re"][9])))
                    ra11_ra15 = 'R11:' + str(int(float(self.measure_dict["Re"][10]))) + ',R12:' + str(
                        int(float(self.measure_dict["Re"][11]))) \
                                + ',R13:' + str(int(float(self.measure_dict["Re"][12]))) + ',R14:' + str(
                        int(float(self.measure_dict["Re"][13]))) + ',R15:' + str(
                        int(float(self.measure_dict["Re"][14])))

                    self.ui.lineEdit_Measure_Re_1_to_5.setText(str(ra1_ra5))
                    self.ui.lineEdit_Measure_Re_6_to_10.setText(str(ra6_ra10))
                    self.ui.lineEdit_Measure_Re_11_to_15.setText(str(ra11_ra15))

                    self.ui.textBrowser_message.append(
                        "\n[INFO] %s \n【---------------------【CS-LM01测量结束】------------------------】\n" % (
                            time.strftime("%Y-%m-%d %H:%M:%S", time.gmtime())))

                    self.ui.pushButton_measure500A.setEnabled(True)
                except Exception as err:
                    self.ui.textBrowser_message.append(
                        "\n[INFO] %s \n【---------------------【CS-LM01测量失败】------------------------】\n%s" % (
                            time.strftime("%Y-%m-%d %H:%M:%S", time.gmtime()), err))
                    self.signal.error.emit("错误", "CS-LM01环境异常！")
                finally:
                    self.ui.pushButton_measure500A.setEnabled(True)

            # elif self.ui.measure_comboBox.currentText() == "CS-LM01IR":
            #     print('data:', 'CS-LM01IR')
            #     pass
            # measure500AThread = Thread(target=measureCL500ARun)
            # measure500AThread.start()

        self.ui.pushButton_measure500A.clicked.connect(measureCL500A)

        def pushButton_loop_run_click():
            def loop_run_click():
                # 判断将数据发给光源
                print('run')
                #  light_data = (self.ui.tableWidget.item(current_line, 2).text()).replace(' ', '')
                case_line = self.ui.tableWidget_loop.currentIndex().row()
                if case_line == -1:
                    self.signal.error.emit("错误", "选择需要运行的条件！")

                    return False
                #需要判断什么样的选择
                choose = (
                    (self.ui.tableWidget_loop.item(case_line, 4).text().replace(' ', '')).replace('[', '').replace(']',
                                                                                                         '')).split(
                    ',')
                print(choose, type(choose))

                if choose[0] == '1':
                    print(' 1 ok')
                    self.ui.checkBox_ch1.setChecked(True)
                else:
                    self.ui.checkBox_ch1.setChecked(False)
                    print(' 1 not ok')
                if choose[1] == '1':
                    print(' 2 ok')
                    self.ui.checkBox_ch2.setChecked(True)
                else:
                    self.ui.checkBox_ch2.setChecked(False)
                if choose[2] == '1':
                    print(' 3 ok')
                    self.ui.checkBox_ch3.setChecked(True)
                else:
                    self.ui.checkBox_ch3.setChecked(False)
                if choose[3] == '1':
                    print(' 2 ok')
                    self.ui.checkBox_ch4.setChecked(True)
                else:
                    self.ui.checkBox_ch4.setChecked(False)
                if choose[4] == '1':
                    self.ui.checkBox_ch5.setChecked(True)
                else:
                    self.ui.checkBox_ch5.setChecked(False)
                if choose[5] == '1':
                    self.ui.checkBox_ch6.setChecked(True)
                else:
                    self.ui.checkBox_ch6.setChecked(False)


                light = self.ui.tableWidget_loop.item(case_line, 2).text()
                sun_light = self.ui.tableWidget_loop.item(case_line, 3).text()
                print(light, sun_light)

               # self.serial.write(bytes.fromhex(light))
                # 需要判断选择了几组光源
                if self.ui.checkBox_ch1.isChecked() == True:
                    self.serial.write(bytes.fromhex(light))
                if self.ui.checkBox_ch2.isChecked() == True:
                    self.serial_2.write(bytes.fromhex(light))
                if self.ui.checkBox_ch3.isChecked() == True:
                    self.serial_3.write(bytes.fromhex(light))
                if self.ui.checkBox_ch4.isChecked() == True:
                    self.serial_4.write(bytes.fromhex(light))
                if self.ui.checkBox_ch5.isChecked() == True:
                    self.serial_5.write(bytes.fromhex(light))
                if self.ui.checkBox_ch6.isChecked() == True:
                    self.serial_6.write(bytes.fromhex(light))

                self.dmx.set_data(1, int(sun_light) + 100)

                # self.loop_run_status = True
                # # self.ui.pushButton_loop_run.setEnabled(False)
                # # global path
                # # workbook = load_workbook(path)
                # # worksheet = workbook.active
                # # print('worksheet:',worksheet)
                # self.ui.pushButton_loop_run.setEnabled(False)
                #
                # for m in range(0, 200):
                #     # cell_num = "C" + str(m + 1)
                #     # data_illum = worksheet[cell_num].value
                #     print('self.ui.tableWidget_loop.item(m, 3):', self.ui.tableWidget_loop.item(m, 3).text(),
                #           type(self.ui.tableWidget_loop.item(m, 3).text()))
                #     if self.ui.tableWidget_loop.item(m, 3).text() == 'None':
                #         data_illum = None
                #         print('data_illum none:', data_illum)
                #     else:
                #         data_illum = self.ui.tableWidget_loop.item(m, 3).text()
                #         print('-------data_illum:------', data_illum)
                #     if self.ui.tableWidget_loop.item(m, 2).text() == 'None':
                #         data_delay = None
                #         print('data_delay none:', data_delay)
                #     else:
                #         data_delay = self.ui.tableWidget_loop.item(m, 2).text()
                #         print('-------data_delay:------', data_delay)
                #     if data_delay == None or data_illum == None:
                #         print('------推出-----------')
                #         break
                #     else:
                #         # 发送数据
                #         print('----m------:', m, data_illum)
                #         self.serial.write(bytes.fromhex(data_illum))
                #         time.sleep(int(data_delay))
                #         print('------data--------:', data_illum, data_delay)
                #         if self.loop_run_status == False:
                #             self.ui.pushButton_loop_run.setEnabled(True)
                #             return False
                #
                # self.ui.pushButton_loop_run.setEnabled(True)

            mearsureThread1 = Thread(target=loop_run_click)
            mearsureThread1.start()

        self.ui.pushButton_loop_run.clicked.connect(pushButton_loop_run_click)

        def matchSpd():
            # try:
            # 匹配关键步骤
            # 统计参与匹配的信息
            check_flag = [checkbox.isChecked() for checkbox in self.spd_checkBox_list[:-1]]
            # 手动屏蔽第3通道
            # check_flag[2] = False
            # led 最大通道的array,shape->(-1,self.spd_num)
            tempMax = self.led_data_max_channel
            # shape->(spd_num,-1)
            tempMax = tempMax.T
            match_led_data = []

            for flag, led_spd in zip(check_flag, tempMax):
                if flag == True:
                    match_led_data.append(led_spd)

            # shape -> (-1,select_num)
            # 2024-11-04  不同设备的单位转换
            match_led_data = np.reshape(match_led_data, (np.sum(check_flag), -1)).T / 1  # 10000
            # buttonGroup_choose_spd
            choose_name = self.ui.buttonGroup_choose_spd.checkedButton().text()
            if choose_name == "Other":
                path = self.ui.lineEdit_MatchSpdPath.text()
                match_std_data = np.loadtxt(path, delimiter=",")
            else:
                match_std_data = self.std_ledData_dict[choose_name]
                match_std_data = self.std_ledData_dict[choose_name]
            print("match_std_data.size:", np.size(match_std_data))
            # 必须是当前350-1100的光谱
            LEDFunc = interp1d(np.linspace(350, 1100, np.size(match_std_data)), match_std_data, "linear")
            insertSTDData = LEDFunc(np.arange(350, 1101))
            insertSTDData = insertSTDData / np.max(insertSTDData)
            match_std_data = insertSTDData
            match_method = self.ui.comboBox_MatchMethod.currentText()
            match_accuracy = self.ui.spinBox_SpdAccuracy.value()
            match_tagetLuminance = self.ui.spinBox_tagetLuminance.value()
            a, newSpd, insertLEDData, insertSTDData, T, XYZ, x, y, cd, scale = m_matchSpd(match_led_data[10:431,:],
                                                                                          match_std_data[10:431],
                                                                                          match_accuracy,
                                                                                          match_tagetLuminance,
                                                                                          match_method)

            # 必须是当前350-1100的光谱
            LEDFunc = interp1d(np.linspace(350, 1100, np.size(match_std_data)), match_std_data, "linear")
            insertSTDData = LEDFunc(np.arange(350, 1101))
            insertSTDData = insertSTDData / np.max(insertSTDData)

            self.ui.textBrowser_message.append(
                "\n[INFO] %s \n匹配完毕!\n" % (time.strftime("%Y-%m-%d %H:%M:%S", time.gmtime())))
            # 显示匹配完毕
            #            QMessageBox.information(self, 'Message', self.info, QMessageBox.Ok)

            # 整理权重参与
            self.slider_draw = False
            indexs = np.where([not x for x in check_flag])[0]
            # a_list = np.zeros((len(check_flag),)).tolist()

            a_list = list(a)

            for index in indexs:
                a_list.insert(index, 0)

            print("a_list:", a_list)
            if self.ui.radioButton_send_true.isChecked():
                # 需要发送数
                correct_a = [func(a0) for a0, func in zip(a_list, self.linear_func)]
                print("correct_a:", correct_a)
                self.current_protocol = self.protocol[self.ui.comboBox_protocol.currentText()]
                for channel, a_ in enumerate(correct_a):
                    self.current_protocol = getOrder(self.current_protocol, channel + 1, a_)
                    self.spd_slider_list[channel].setValue(int(a_ * 1000))

                self.ui.textBrowser_message.append(
                    "\n[SEND] %s \n%s" % (time.strftime("%Y-%m-%d %H:%M:%S", time.gmtime()), self.current_protocol))
                # 判断是否控制那个光源
                if self.ui.checkBox_ch1.isChecked() == True:
                    self.serial.write(bytes.fromhex(self.current_protocol))
                    print('ok')
                else:
                    print('not ok')

                if self.ui.checkBox_ch2.isChecked() == True:
                    self.serial_2.write(bytes.fromhex(self.current_protocol))
                    print('ok2')
                else:
                    print('not ok2')

                if self.ui.checkBox_ch3.isChecked() == True:
                    self.serial_3.write(bytes.fromhex(self.current_protocol))
                    print('ok3')
                else:
                    print('not ok3')

                if self.ui.checkBox_ch4.isChecked() == True:
                    self.serial_4.write(bytes.fromhex(self.current_protocol))
                    print('ok4')
                else:
                    print('not ok4')

                if self.ui.checkBox_ch5.isChecked() == True:
                    self.serial_5.write(bytes.fromhex(self.current_protocol))
                    print('ok5')
                else:
                    print('not ok5')

                if self.ui.checkBox_ch6.isChecked() == True:
                    self.serial_6.write(bytes.fromhex(self.current_protocol))
                    print('ok6')
                else:
                    print('not ok6')

                print('self.current_protocol:', self.current_protocol)

            # 更新界面
            self.current_wave = np.sum(self.led_data_max_channel * np.asarray(a_list), 1)
            self.match_wave = insertSTDData
            self.graphUpdate(np.arange(751) + 350, self.current_wave, self.match_wave, self.measure_wave)
            self.slider_draw = True
            # except Exception as err:
            #     self.ui.textBrowser_message.append("\n[ERROR] %s \n匹配失败!\n %s" % (time.strftime("%Y-%m-%d %H:%M:%S",time.gmtime()),err))

        self.ui.pushButton_matchStart.clicked.connect(matchSpd)

        # 荧光灯管控制
        def tl84_switch():
            print('tl84 ok')
            if self.ui.tl84_switch_pushButton.text() == '打开':
                send_data = '01 00 00 01 00 64 29 0d 0d 0a'
                self.serial.write(bytes.fromhex(send_data))
                self.ui.tl84_switch_pushButton.setText('关闭')
            elif self.ui.tl84_switch_pushButton.text() == '关闭':
                send_data = '01 00 00 01 00 00 29 0d 0d 0a'
                self.serial.write(bytes.fromhex(send_data))
                self.ui.tl84_switch_pushButton.setText('打开')

        self.ui.tl84_switch_pushButton.clicked.connect(tl84_switch)

        # 控件复位
        def pushButton_clear_spec_click():
            # print('pushButton_clear_spec ok')
            self.spd_num = self.setting["channels_num"]
            # print('self.spd_num:', self.spd_num)
            for i in range(0, self.spd_num + 1):
                self.spd_slider_list[i].setValue(int(0.0))

            # 重置时清空测量数据和校正状态
            self.measure_wave = []
            self.measure_wave_1 = []
            self.correction_enabled = False
            self.ui.pushButton_correction.setEnabled(False)

            # 清空图表
            self.graphUpdate(np.arange(751) + 350, self.current_wave, self.match_wave, [])

        self.ui.pushButton_clear_spec.clicked.connect(pushButton_clear_spec_click)

        # 校正按钮
        def pushButton_correction_click():
            """校正按钮点击事件"""
            if not self.correction_enabled or len(self.measure_wave_1) == 0:
                self.signal.warning.emit("警告", "请先进行测量后再执行校正!")
                return

            if len(self.match_wave) == 0:
                self.signal.warning.emit("警告", "请先导入或选择目标光谱!")
                return

            try:
                # 获取当前31个通道的电压值
                current_voltages = []
                for i in range(self.spd_num):
                    voltage = self.spd_slider_list[i].value()  # 0-1000
                    current_voltages.append(float(voltage))

                # 初始化校正器
                corrector = SpectrumCorrection(self.led_data_max_channel)

                # 执行校正
                corrected_voltages, error = corrector.correct_spectrum(
                    current_voltages,
                    self.measure_wave_1,
                    self.match_wave
                )

                # 更新滑块值
                for i, voltage in enumerate(corrected_voltages):
                    if i < len(self.spd_slider_list) - 1:  # 排除最后一个总调控滑块
                        self.spd_slider_list[i].setValue(int(voltage))
                        self.spd_lineEdit_list[i].setText(str(voltage / 10))

                # 更新当前光谱显示
                self.current_wave = np.sum(self.led_data_max_channel * np.array(corrected_voltages) / 1000.0, 1)
                self.graphUpdate(np.arange(751) + 350, self.current_wave, self.match_wave, self.measure_wave_1)

                # 记录校正日志
                save_correction_log(corrected_voltages, error, 1)

                # 显示校正结果
                self.ui.textBrowser_message.append(
                    f"\n[INFO] {time.strftime('%Y-%m-%d %H:%M:%S', time.gmtime())} \n校正完成! 误差: {error:.6f}")
                self.ui.textBrowser_message.moveCursor(self.ui.textBrowser_message.textCursor().End)

            except Exception as e:
                self.signal.error.emit("错误", f"校正过程中发生错误: {str(e)}")
                self.ui.textBrowser_message.append(
                    f"\n[ERROR] {time.strftime('%Y-%m-%d %H:%M:%S', time.gmtime())} \n校正失败: {str(e)}")
                self.ui.textBrowser_message.moveCursor(self.ui.textBrowser_message.textCursor().End)

        self.ui.pushButton_correction.clicked.connect(pushButton_correction_click)

        # cwf 开关
        def cwf_switch():
            print('tl84 ok')
            if self.ui.cwf_switch_pushButton.text() == '打开':
                send_data = '01 00 00 02 00 64 29 0d 0d 0a'
                self.serial.write(bytes.fromhex(send_data))
                self.ui.cwf_switch_pushButton.setText('关闭')
            elif self.ui.cwf_switch_pushButton.text() == '关闭':
                send_data = '01 00 00 02 00 00 29 0d 0d 0a'
                self.serial.write(bytes.fromhex(send_data))
                self.ui.cwf_switch_pushButton.setText('打开')

        self.ui.cwf_switch_pushButton.clicked.connect(cwf_switch)

        def tl84_send():
            num = Light_Control.hex_data_4(self.ui.tl84_spinBox.value())
            print('num:', num)
            send_data = '01 00 00 01 ' + num + '29 0d 0d 0a'
            self.serial.write(bytes.fromhex(send_data))
            print('tl84:', send_data)

        self.ui.tl84_send_pushButton.clicked.connect(tl84_send)

        # CWF发松
        def cwf_send():
            num = Light_Control.hex_data_4(self.ui.cwf_spinBox.value())
            print('cwf num:', num)
            send_data = '01 00 00 02 ' + num + '29 0d 0d 0a'
            self.serial.write(bytes.fromhex(send_data))
            print('cwf:', send_data)

        self.ui.cwf_send_pushButton.clicked.connect(cwf_send)

    def init_window_signal(self):
        def emitError(str1, str2):
            QMessageBox.critical(self.ui, str1, str2)

        def emitNote(str1, str2):
            QMessageBox.information(self.ui, str1, str2)

        def emitWarning(str1, str2):
            QMessageBox.warning(self.ui, str1, str2)

        def enableCorrection():
            """启用校正按钮"""
            self.correction_enabled = True
            self.ui.pushButton_correction.setEnabled(True)
            print("校正按钮已启用")

        self.signal = Signal_View()
        self.signal.error.connect(emitError)
        self.signal.note.connect(emitNote)
        self.signal.warning.connect(emitWarning)
        self.signal.enable_correction.connect(enableCorrection)

    def init_protocol(self):
        self.protocol = self.setting["protocol"]
        keys = self.protocol.keys()
        for key in keys:
            self.protocol[key] += " 00" * (self.spd_num) * 2
            self.protocol[key] += " 29 0d 0d 0a"
        self.ui.comboBox_protocol.addItems(keys)

    def init_comboBox_ledData(self):
        led_data_names = os.listdir("./ledData")
        self.ui.comboBox_ledData.addItems(led_data_names)
        self.ui.comboBox_ledData.setCurrentIndex(-1)

        def read_led_data():
            self.led_data_arrays = []
            self.linear_func = []
            self.linear_current = []
            self.linear_light = []
            self.led_data_max_channel = []

            led_name = self.ui.comboBox_ledData.currentText()
            led_dir = os.path.join("./ledData", led_name)
            led_channels = os.listdir(led_dir)
            led_channels = [x for x in led_channels if x.endswith(".csv")]
            led_channels = sorted(led_channels)
            led_paths = [os.path.join(led_dir, x) for x in led_channels]

            if len(led_paths) != self.spd_num:
                if self.ui.comboBox_ledData.currentIndex() != -1:
                    self.ui.textBrowser_message.append("\n[ERROR] %s \n通道数不一致!\n设置:%d\n导入:%d" % (
                        time.strftime("%Y-%m-%d %H:%M:%S", time.gmtime()), self.spd_num, len(led_paths)))
                    self.ui.textBrowser_message.moveCursor(self.ui.textBrowser_message.textCursor().End)
                    self.signal.error.emit("错误!", "通道数不一致!\n设置:%d\n导入:%d" % (self.spd_num, len(led_paths)))
                self.ui.comboBox_ledData.setCurrentIndex(-1)

            else:
                self.ui.textBrowser_message.append("\n[INFO] %s \n导入数据成功! 路径:%s" % (
                    time.strftime("%Y-%m-%d %H:%M:%S", time.gmtime()), led_dir))
                self.ui.textBrowser_message.moveCursor(self.ui.textBrowser_message.textCursor().End)
                for spd, path in enumerate(led_paths):
                    led_data = np.loadtxt(path, delimiter=",")
                    self.led_data_arrays.append(led_data)
                    # 界面更改
                    led_data = led_data[1:, 0]
                    self.led_data_max_channel.append(led_data)

                    peek = np.argmax(led_data) + 350
                    self.spd_checkBox_list[spd].setText(str(peek))
                    rgb = wavelength_to_rgb(peek)
                    html = RGBToHTMLColor(rgb)
                    self.spd_checkBox_list[spd].setStyleSheet("color:#FFFFFF;" + "background-color:" + html)

                #  print('html:', html)
                # 白光单独修改
                '''
                self.spd_checkBox_list[23].setText("727")
                self.spd_checkBox_list[23].setStyleSheet("color:#FFFFFF;;" + "background-color:" + "#000000")
                self.spd_checkBox_list[24].setText("740")
                self.spd_checkBox_list[24].setStyleSheet("color:#FFFFFF;" + "background-color:" + "#000000")
                self.spd_checkBox_list[25].setText("778")
                self.spd_checkBox_list[25].setStyleSheet("color:#FFFFFF;" + "background-color:" + "#000000")
                self.spd_checkBox_list[26].setText("850")
                self.spd_checkBox_list[26].setStyleSheet("color:#FFFFFF;" + "background-color:" + "#000000")
                self.spd_checkBox_list[27].setText("940")
                self.spd_checkBox_list[27].setStyleSheet("color:#FFFFFF;" + "background-color:" + "#000000")

                self.spd_checkBox_list[28].setText("1050")
                self.spd_checkBox_list[28].setStyleSheet("color:#FFFFFF;" + "background-color:" + "#000000")
                '''
                # 所有的led最大值数据
                self.led_data_max_channel = np.reshape(self.led_data_max_channel, (self.spd_num, -1)).T
                # 导入后线性化数据
                self.linear()
                self.spd_checkBox_list[self.spd_num].setText("all")

                # 初始化校正器
                self.spectrum_corrector = SpectrumCorrection(self.led_data_max_channel)

        self.ui.comboBox_ledData.currentIndexChanged.connect(read_led_data)

    def linear(self):
        # todo:进行线性化输出
        for led_data in self.led_data_arrays:
            linear_func, linear_current, linear_light = getFunc(led_data)
            self.linear_func.append(linear_func)
            self.linear_current.append(linear_current)
            self.linear_light.append(linear_light)
            self.linear_current_d.append(linear_func(linear_light))
            # plt.plot(linear_current,linear_light,marker="s")
            # plt.plot(linear_func(np.arange(100)/100),np.arange(100)/100,marker="*")
            # plt.xlabel("light")
            # plt.ylabel("current")
            # plt.show()
        # self.signal.note.emit("提示","线性化数据已保存")
        self.ui.textBrowser_message.append(
            "\n[INFO] %s \n线性化校准完毕,数据已保存!" % (time.strftime("%Y-%m-%d %H:%M:%S", time.gmtime())))
        self.ui.textBrowser_message.moveCursor(self.ui.textBrowser_message.textCursor().End)

    def init_QtGraph(self):
        self.pw = pg.PlotWidget()
        self.pw.setBackground('#FFFFFF')  ##666666
        self.p1 = self.pw.plotItem
        self.p2 = pg.ViewBox()
        self.p1.setLabels(left='Spd Value')
        self.p1.showAxis('right')
        self.p1.scene().addItem(self.p2)
        # p1.getAxis('right').linkToView(p2)
        self.p2.setXLink(self.p1)
        # p1.getAxis('right').setLabel('axis2', color='#0000ff')
        self.p1.vb.sigResized.connect(self.updateViews)
        target_spd = self.std_ledData_dict["A"]

        # 界面显示
        x = np.linspace(350, 1100, np.size(target_spd))
        f = interp1d(x, target_spd)
        x = np.linspace(350, 1100, 751)
        y = f(x)
        self.match_wave = y
        self.graphUpdate(x, self.current_wave, self.match_wave, self.measure_wave)

        # Grid = pg.GridItem()
        # Grid.setPen(color='g',width=0.4,style=QtCore.Qt.DashLine)       # 网格线的风格（颜色、粗细、风格）
        # Grid.setTickSpacing(x=[5])     # 网格线之间的间隔
        # Grid.setTextPen(color='r')          # 网格刻度值的风格设置
        # self.p1.vb.addItem(Grid)

    def updateViews(self):
        ## view has resized; update auxiliary views to match
        self.p2.setGeometry(self.p1.vb.sceneBoundingRect())
        ## need to re-update linked axes since this was called
        ## incorrectly while views had different shapes.
        ## (probably this should be handled in ViewBox.resizeEvent)
        self.p2.linkedViewChanged(self.p1.vb, self.p2.XAxis)

    def graphUpdate(self, spd_x, light_wave=[], match_wave=[], measure_wave=[]):
        """
        主窗口更新
        """
        print("match_wave.size:", np.size(match_wave))
        self.p1.clear()
        self.updateViews()
        spd_x = np.linspace(350, 1100, 751)
        # if (np.size(light_wave) < 751):
        #     addList = np.zeros((751 - np.size(light_wave)-10))
        #     light_wave = np.hstack([np.zeros((10)),light_wave,addList]).reshape((-1,))

        # if (np.size(match_wave) < 751):
        #     addList = np.zeros((751 - np.size(match_wave)-10))
        #     match_wave = np.hstack([np.zeros((10)),match_wave,addList]).reshape((-1,))

        # if (np.size(measure_wave) < 751):
        #     addList = np.zeros((751 - np.size(measure_wave)-10))
        #     measure_wave = np.hstack([np.zeros((10)),measure_wave,addList]).reshape((-1,)) # [10:431]

        if np.size(light_wave) != 0:
            self.p1.plot(spd_x, light_wave / np.max(light_wave), pen=pg.mkPen(width=2, color="#0000ff"), name="Match")
        if np.size(match_wave) != 0:
            self.p1.plot(spd_x, match_wave / np.max(match_wave), pen=pg.mkPen(width=2, color="#00ff00"), name="Target")
        if np.size(measure_wave) != 0:
            # 处理测量数据：先保存原始数据，然后进行分段归一化
            measure_wave_processed = self.process_measure_wave(measure_wave)
            self.p1.plot(spd_x, measure_wave_processed, pen=pg.mkPen(width=2, color="#ff0000"),
                         name="Measure")

            np.savetxt("spd_x.csv", spd_x, delimiter=",")
            np.savetxt("light_wave.csv", light_wave, delimiter=",")
            np.savetxt("match_wave.csv", match_wave, delimiter=",")
            # 保存原始测量数据
            np.savetxt("measure_wave.csv", measure_wave, delimiter=",")
            # 保存处理后的测量数据
            np.savetxt("measure_wave_1.csv", measure_wave_processed, delimiter=",")


        y = self.p1.getAxis('bottom')
        # x = list(np.linspace(360, 780, 43))
        x = list(np.linspace(350, 1100, 76))
        strs = [str(int(x0)) for x0 in x]
        ticks = [[i, j] for i, j in zip(x, strs)]
        y.setTicks([ticks])
        self.p1.addLegend()
        self.p1.showGrid(x=True, y=True)
        self.p1._updateView()
        vbox = QHBoxLayout()
        vbox.addWidget(self.pw)
        # 设置全局layout
        self.ui.groupBox_spd_plot.setLayout(vbox)

    def process_measure_wave(self, measure_wave):
        """
        处理测量数据：只对780nm及之前的数据进行归一化，780nm之后的数据保持不变

        Args:
            measure_wave: 原始测量数据 (751个点，对应350-1100nm)

        Returns:
            处理后的测量数据
        """
        if len(measure_wave) == 0:
            return measure_wave

        measure_wave = np.array(measure_wave)
        processed_wave = measure_wave.copy()

        # 计算780nm对应的索引 (350-1100nm, 1nm间隔)
        # 780nm对应索引: (780-350) = 430
        nm_780_index = 780 - 350

        if nm_780_index < len(measure_wave):
            # 只对780nm及之前的数据进行归一化
            before_780 = measure_wave[:nm_780_index + 1]
            after_780 = measure_wave[nm_780_index + 1:]

            # 归一化780nm及之前的数据
            if np.max(before_780) > 0:
                before_780_normalized = before_780 / np.max(before_780)
            else:
                before_780_normalized = before_780

            # 合并数据：归一化的前半部分 + 原始的后半部分
            processed_wave = np.concatenate([before_780_normalized, after_780])
        else:
            # 如果数据长度不足780nm，则全部归一化
            if np.max(measure_wave) > 0:
                processed_wave = measure_wave / np.max(measure_wave)

        # 保存处理后的数据到实例变量
        self.measure_wave_1 = processed_wave

        return processed_wave

    def init_tableWidget(self):
        # 测试仪器
        self.ui.measure_comboBox.addItems(['CL-500A', 'CS-LM01', 'CS-LM01IR'])

        self.ui.tableWidget.setColumnCount(4)
        self.ui.tableWidget.setRowCount(80)
        self.ui.tableWidget.setColumnWidth(0, 65)  # 设置j列的宽度
        self.ui.tableWidget.setColumnWidth(1, 100)  # 设置j列的宽度
        self.ui.tableWidget.setColumnWidth(2, 500)  # 设置j列的宽度
        self.ui.tableWidget.setColumnWidth(3, 200)
        self.ui.tableWidget.setHorizontalHeaderLabels(['编号', '名称', '数据', '选择'])
        self.ui.tableWidget.verticalHeader().setVisible(False)  # 隐藏垂直表头
        for i in range(0, 81):
            self.ui.tableWidget.setItem(i, 0, QTableWidgetItem('P' + str(i + 1)))

        # 选择全行
        self.ui.tableWidget.setSelectionBehavior(QAbstractItemView.SelectRows)

        for i in range(0, 80):
            data = write_data.read_light_json('name' + str(i))
            self.ui.tableWidget.setItem(i, 1, QTableWidgetItem(str(data)))
            data = write_data.read_light_json('data' + str(i))
            self.ui.tableWidget.setItem(i, 2, QTableWidgetItem(str(data)))
            data = write_data.read_light_json('choose' + str(i))
            self.ui.tableWidget.setItem(i, 3, QTableWidgetItem(str(data)))

        # 初始化 loop 表格
        self.ui.tableWidget_loop.setColumnCount(5)
        self.ui.tableWidget_loop.setRowCount(80)
        self.ui.tableWidget_loop.setColumnWidth(0, 50)  # 设置j列的宽度
        self.ui.tableWidget_loop.setColumnWidth(1, 200)  # 设置j列的宽度
        self.ui.tableWidget_loop.setColumnWidth(2, 100)  # 设置j列的宽度
        self.ui.tableWidget_loop.setColumnWidth(3, 200)  # 设置j列的宽度
        self.ui.tableWidget_loop.setColumnWidth(4, 200)  # 设置j列的宽度
        self.ui.tableWidget_loop.verticalHeader().setVisible(False)  # 隐藏垂直表头

        self.ui.tableWidget_loop.setHorizontalHeaderLabels(['编号', '备注', '数据1', '数据2','选择'])

        for i in range(0, 81):
            self.ui.tableWidget_loop.setItem(i, 0, QTableWidgetItem('P' + str(i + 1)))

        for i in range(0, 200):
            data = write_data.read_light_json('case_beizhu' + str(i))
            self.ui.tableWidget_loop.setItem(i, 1, QTableWidgetItem(str(data)))
            data = write_data.read_light_json('case_light' + str(i))
            self.ui.tableWidget_loop.setItem(i, 2, QTableWidgetItem(str(data)))
            data = write_data.read_light_json('case_sun_light' + str(i))
            self.ui.tableWidget_loop.setItem(i, 3, QTableWidgetItem(str(data)))
            data = write_data.read_light_json('case_choose_light' + str(i))
            self.ui.tableWidget_loop.setItem(i, 4, QTableWidgetItem(str(data)))
            # print('-------------:', i)

    def init_ratioButtonGroup(self):
        def choose():
            choose_name = self.ui.buttonGroup_choose_spd.checkedButton().text()
            self.ui.textBrowser_message.append("\n[INFO] %s \n匹配选项按钮组选择:%s" % (
                time.strftime("%Y-%m-%d %H:%M:%S", time.gmtime()), choose_name))
            self.ui.textBrowser_message.moveCursor(self.ui.textBrowser_message.textCursor().End)
            if choose_name == "Other":
                self.ui.lineEdit_MatchSpdPath.setEnabled(True)
                self.ui.pushButton_MatchSpdImport.setEnabled(True)
                self.ui.pushButton_MatchSpdMeasure.setEnabled(True)
            else:
                self.ui.lineEdit_MatchSpdPath.setEnabled(False)
                self.ui.pushButton_MatchSpdImport.setEnabled(False)
                self.ui.pushButton_MatchSpdMeasure.setEnabled(False)
                target_spd = self.std_ledData_dict[choose_name]

                # 界面显示
                x = np.linspace(350, 1100, np.size(target_spd))
                f = interp1d(x, target_spd)
                x = np.linspace(350, 1100, 751)
                y = f(x)
                self.match_wave = y
                self.graphUpdate(x, self.current_wave, self.match_wave, self.measure_wave)

        self.ui.buttonGroup_choose_spd.buttonClicked.connect(choose)

    # TODO 保存光源数据
    def pushButton_saveOrder_click(self):
        def saveOrder_click():  # line = self.tableWidget.currentIndex().row()
            # print('data:',self.current_protocol)
            print('self.current_protocol:', self.current_protocol)
            if self.current_protocol == None:
                pass
            else:
                current_line = self.ui.tableWidget.currentIndex().row()
                self.ui.tableWidget.setItem(int(current_line), 2, QTableWidgetItem(self.current_protocol))

            # 获取当前的check 保存到table
            if self.ui.checkBox_ch1.isChecked() == True:
                light_check_status_1 = 1
            else:
                light_check_status_1 = 0
            if self.ui.checkBox_ch2.isChecked() == True:
                light_check_status_2 = 1
            else:
                light_check_status_2 = 0
            if self.ui.checkBox_ch3.isChecked() == True:
                light_check_status_3 = 1
            else:
                light_check_status_3 = 0
            if self.ui.checkBox_ch4.isChecked() == True:
                light_check_status_4 = 1
            else:
                light_check_status_4 = 0
            if self.ui.checkBox_ch5.isChecked() == True:
                light_check_status_5 = 1
            else:
                light_check_status_5 = 0
            if self.ui.checkBox_ch6.isChecked() == True:
                light_check_status_6 = 1
            else:
                light_check_status_6 = 0

            light_check_status_all = [light_check_status_1, light_check_status_2, light_check_status_3,
                                      light_check_status_4, light_check_status_5, light_check_status_6]

            print('light_check_status_all:', light_check_status_all)
            self.ui.tableWidget.setItem(int(current_line), 3, QTableWidgetItem(str(light_check_status_all)))

            # 保存数据
            for i in range(0, 81):
                if self.ui.tableWidget.item(i, 1) == None:
                    pass
                else:
                    data = self.ui.tableWidget.item(i, 1).text()
                    write_data.write_light_json('name' + str(i), data)
                if self.ui.tableWidget.item(i, 2) == None:
                    pass
                else:
                    data = self.ui.tableWidget.item(i, 2).text()
                    write_data.write_light_json('data' + str(i), data)
                    print('-----保存数据-----', i)

                if self.ui.tableWidget.item(i, 3) == None:
                    pass
                else:
                    data = self.ui.tableWidget.item(i, 3).text()
                    write_data.write_light_json('choose' + str(i), data)
                    print('-----保存数据-----', i)

            self.ui.textBrowser_message.append(
                "\n[INFO] %s \n保存数据完成!" % (time.strftime("%Y-%m-%d %H:%M:%S", time.gmtime())))
            self.ui.textBrowser_message.moveCursor(self.ui.textBrowser_message.textCursor().End)
            self.signal.note.emit("消息", "保存数据完成! ")

        self.ui.pushButton_saveOrder.clicked.connect(saveOrder_click)

    # 导入csv数据
    def pushButton_importOrder_2_click(self):
        def pushButton_importOrder_2_temp():
            # print('导入数据')
            global path_import
            FileDirectory = QFileDialog.getOpenFileNames(QMainWindow(), "选择文件夹", "*.xlsx")  # 选择目录，返回选中的路径
            # print('-------:', FileDirectory, type(FileDirectory), FileDirectory[0][0])
            #  return False
            path_import = str(FileDirectory[0][0])
            workbook = load_workbook(path_import)
            worksheet = workbook.active
            print('00000000000')

            # for i in range(0, 80):
            #     cell_num = "A" + str(i+1)
            #     cell_value = worksheet[cell_num].value
            #     print('cell_value:', cell_value)
            #     if cell_value == None:
            #         break
            # print('1111111111111111')
            num = 80
            # print('num:', num)

            for m in range(0, num):
                cell_num = "A" + str(m + 1)
                data = worksheet[cell_num].value
                self.ui.tableWidget.setItem(m, 0, QTableWidgetItem(str(data)))

                cell_num = "B" + str(m + 1)
                data = worksheet[cell_num].value
                self.ui.tableWidget.setItem(m, 1, QTableWidgetItem(str(data)))

                cell_num = "C" + str(m + 1)
                data = worksheet[cell_num].value
                self.ui.tableWidget.setItem(m, 2, QTableWidgetItem(str(data)))

            self.ui.textBrowser_message.append(
                "\n[INFO] %s \n导入数据完成!" % (time.strftime("%Y-%m-%d %H:%M:%S", time.gmtime())))
            self.ui.textBrowser_message.moveCursor(self.ui.textBrowser_message.textCursor().End)

        self.ui.pushButton_importOrder_2.clicked.connect(pushButton_importOrder_2_temp)

    def pushButton_loop_save_click(self):
        def loop_save_click():
            #  print(self.ui.tableWidget_loop.item(0, 1).text())
            #  print(self.ui.tableWidget_loop.item(0, 2).text())
            #   print(self.ui.tableWidget_loop.item(0, 3).text())
            #   print(self.ui.tableWidget_loop.item(3, 1).text())
            # 先判断数据 在保存
            print(self.ui.tableWidget.currentIndex().row())

            if self.ui.tableWidget.currentIndex().row() == -1 or self.ui.tableWidget_loop.currentIndex().row() == -1:
                self.signal.error.emit("错误", "选择需要保存的条件！")
                return False

            current_line = self.ui.tableWidget.currentIndex().row()
            light_data = (self.ui.tableWidget.item(current_line, 2).text()).replace(' ', '')

            case_line = self.ui.tableWidget_loop.currentIndex().row()
            print('case_line:',case_line)
            self.ui.tableWidget_loop.setItem(int(case_line), 2, QTableWidgetItem(str(light_data)))
            self.ui.tableWidget_loop.setItem(int(case_line), 3,
                                             QTableWidgetItem(str(self.ui.spinBox_le007_left_illum.value())))

            #获取选择信息
            # 获取当前的check 保存到table
            if self.ui.checkBox_ch1.isChecked() == True:
                light_check_status_1 = 1
            else:
                light_check_status_1 = 0
            if self.ui.checkBox_ch2.isChecked() == True:
                light_check_status_2 = 1
            else:
                light_check_status_2 = 0
            if self.ui.checkBox_ch3.isChecked() == True:
                light_check_status_3 = 1
            else:
                light_check_status_3 = 0
            if self.ui.checkBox_ch4.isChecked() == True:
                light_check_status_4 = 1
            else:
                light_check_status_4 = 0
            if self.ui.checkBox_ch5.isChecked() == True:
                light_check_status_5 = 1
            else:
                light_check_status_5 = 0
            if self.ui.checkBox_ch6.isChecked() == True:
                light_check_status_6 = 1
            else:
                light_check_status_6 = 0

            light_check_status_all = [light_check_status_1, light_check_status_2, light_check_status_3,
                                      light_check_status_4, light_check_status_5, light_check_status_6]

            print('light_check_status_all:', light_check_status_all)
            self.ui.tableWidget_loop.setItem(int(case_line), 4, QTableWidgetItem(str(light_check_status_all)))

            for i in range(0, 200):
                if self.ui.tableWidget_loop.item(i, 1) == None:
                    write_data.write_light_json('case_beizhu' + str(i), None)
                else:
                    data = self.ui.tableWidget_loop.item(i, 1).text()
                    write_data.write_light_json('case_beizhu' + str(i), data)
                if self.ui.tableWidget_loop.item(i, 2) == None:
                    write_data.write_light_json('case_light' + str(i), None)
                else:
                    data = self.ui.tableWidget_loop.item(i, 2).text()
                    write_data.write_light_json('case_light' + str(i), data)

                if self.ui.tableWidget_loop.item(i, 3) == None:
                    write_data.write_light_json('case_sun_light' + str(i), None)
                else:
                    data = self.ui.tableWidget_loop.item(i, 3).text()
                    write_data.write_light_json('case_sun_light' + str(i), data)

                if self.ui.tableWidget_loop.item(i, 4) == None:
                    write_data.write_light_json('case_choose_light' + str(i), None)
                else:
                    data = self.ui.tableWidget_loop.item(i, 4).text()
                    write_data.write_light_json('case_choose_light' + str(i), data)
            print('loop save')

        self.ui.pushButton_loop_save.clicked.connect(loop_save_click)

    # TODO 光源LOOP 导入CSV文件
    def pushButton_loop_import_click(self):
        def loop_import_click():
            global path
            FileDirectory = QFileDialog.getOpenFileNames(QMainWindow(), "选择文件夹", "*.xlsx")  # 选择目录，返回选中的路径
            print('-------:', FileDirectory, type(FileDirectory), FileDirectory[0][0])

            path = str(FileDirectory[0][0])

            workbook = load_workbook(path)
            worksheet = workbook.active

            for i in range(1, 200):
                cell_num = "A" + str(i)
                cell_value = worksheet[cell_num].value
                #    print('cell_value:', cell_value)
                if cell_value == None:
                    break
            num = i - 1
            # print('num:', num)

            for m in range(0, num):
                cell_num = "A" + str(m + 1)
                data = worksheet[cell_num].value
                self.ui.tableWidget_loop.setItem(m, 1, QTableWidgetItem(str(data)))

                cell_num = "B" + str(m + 1)
                data = worksheet[cell_num].value
                self.ui.tableWidget_loop.setItem(m, 2, QTableWidgetItem(str(data)))

                # cell_num = "C" + str(m + 1)
                # data = worksheet[cell_num].value
                # self.ui.tableWidget_loop.setItem(m, 3, QTableWidgetItem(str(data)))

    #        self.ui.pushButton_loop_import.clicked.connect(loop_import_click)

    def pushButton_loop_stop_click(self):
        def loop_stop_click():
            self.loop_run_status = False
            self.ui.pushButton_loop_run.setEnabled(True)
            print('--------False----------')

    #        self.ui.pushButton_loop_stop.clicked.connect(loop_stop_click)

    # TODO 发送数据给光源
    def pushButton_sendLight_click(self):
        def sendLight_click():
            # 当前是哪行 就把哪行数据发给光源
            line = self.ui.tableWidget.currentIndex().row()
            # print('new:', line)
            data = (self.ui.tableWidget.item(line, 2).text()).replace(' ', '')
            print('-----send data:----', data)

            choose = (
                (self.ui.tableWidget.item(line, 3).text().replace(' ', '')).replace('[', '').replace(']', '')).split(
                ',')
            print(choose, type(choose))

            if choose[0] == '1':
                print(' 1 ok')
                self.ui.checkBox_ch1.setChecked(True)
            else:
                self.ui.checkBox_ch1.setChecked(False)
                print(' 1 not ok')
            if choose[1] == '1':
                print(' 2 ok')
                self.ui.checkBox_ch2.setChecked(True)
            else:
                self.ui.checkBox_ch2.setChecked(False)
            if choose[2] == '1':
                print(' 3 ok')
                self.ui.checkBox_ch3.setChecked(True)
            else:
                self.ui.checkBox_ch3.setChecked(False)
            if choose[3] == '1':
                print(' 2 ok')
                self.ui.checkBox_ch4.setChecked(True)
            else:
                self.ui.checkBox_ch4.setChecked(False)
            if choose[4] == '1':
                self.ui.checkBox_ch5.setChecked(True)
            else:
                self.ui.checkBox_ch5.setChecked(False)
            if choose[5] == '1':
                self.ui.checkBox_ch6.setChecked(True)
            else:
                self.ui.checkBox_ch6.setChecked(False)

            # 需要判断选择了几组光源
            if self.ui.checkBox_ch1.isChecked() == True:
                self.serial.write(bytes.fromhex(data))
            if self.ui.checkBox_ch2.isChecked() == True:
                self.serial_2.write(bytes.fromhex(data))
            if self.ui.checkBox_ch3.isChecked() == True:
                self.serial_3.write(bytes.fromhex(data))
            if self.ui.checkBox_ch4.isChecked() == True:
                self.serial_4.write(bytes.fromhex(data))
            if self.ui.checkBox_ch5.isChecked() == True:
                self.serial_5.write(bytes.fromhex(data))
            if self.ui.checkBox_ch6.isChecked() == True:
                self.serial_6.write(bytes.fromhex(data))

            print('len(data)', len(data))
            num = int((len(data) - 12) / 4)

            for i in range(0, num):
                # data[7:11]
                # print('-num-', data[i * 4 + 6:i * 4 + 6 + 4])  # 0
                # print('-num-', data[10:14])#1
                #  pwm =
                #   pwm = #write_data.hex_to_float(data[i*4+6:i*4+6+4])
                pwm = int(data[i * 4 + 6:i * 4 + 6 + 4], 16) / 10
                self.spd_lineEdit_list[i].setText(str(pwm))

            # self.match_wave = [1,2,3]
            # self.graphUpdate(np.arange(421) + 360, self.current_wave, self.match_wave, self.measure_wave)

        self.ui.pushButton_sendLight.clicked.connect(sendLight_click)

    # TODO Flicker 功能
    def pushButton_sendflickLight_click(self):
        def sendflickLight_click():
            print('------:', self.ui.flick_spinBox.value())

            # 写数据给光源
            data_lens = "02 0e 00 01 f4 " + write_data.hex_data_4(self.ui.flick_spinBox.value()) + ' 0d 0a'
            print('data_lens:', data_lens)
            self.serial.write(bytes.fromhex(data_lens))

            self.ui.textBrowser_message.append(
                "\n[INFO] %s \nFlicker数据发送完成!" % (time.strftime("%Y-%m-%d %H:%M:%S", time.gmtime())))
            self.ui.textBrowser_message.moveCursor(self.ui.textBrowser_message.textCursor().End)

        self.ui.pushButton_sendflickLight.clicked.connect(sendflickLight_click)

    # TODO 写数据给光源
    def pushButton_saveOrder_to_light_click(self):
        def saveOrder_to_light_click():
            # 需要发送的数据
            data_list = []
            for i in range(0, 80):
                data = self.ui.tableWidget.item(i, 1).text() + self.ui.tableWidget.item(i, 2).text()
                data_list.append(data)
            data = str(data_list).replace('[', '').replace(']', '').replace(' ', '').replace("'", '').replace(',', '')
            print('data:', len(data), data)
            need_send_data = data
            print('need_send_data:', need_send_data)

            # start_command = "02 00 00 "+need_send_data
            # print('----send comamd:--------', start_command)
            # self.serial.write(bytes.fromhex(start_command))

            '''
            start_command = "02"+ need_send_data
            print('----send comamd:--------', start_command)
            self.serial.write(start_command.encode('utf8'))
            '''

            # left_ser.write("OFF".encode("utf8"))
            # 获取需要发送的数据
            start_command = "52 58 44 0d 0a"
            print('start:', start_command)
            self.serial.write(bytes.fromhex(start_command))
            time.sleep(0.5)
            print('len:', len(need_send_data))
            data_lens = "43 4f 4e" + write_data.hex_data(len(need_send_data))
            print('len:', data_lens)
            self.serial.write(bytes.fromhex(data_lens))

            self.spd_num = self.setting["channels_num"]
            print('self.spd_num:', self.spd_num)

            # 21 通道存储方式
            if self.spd_num == 21:
                time.sleep(0.5)
                self.serial.write(need_send_data[0:1024].encode('utf-8'))
                time.sleep(0.5)  # 7600
                self.serial.write(need_send_data[1024:2048].encode('utf-8'))  # 1024 1995
                time.sleep(0.5)  # 7600
                self.serial.write(need_send_data[2048:3072].encode('utf-8'))
                time.sleep(0.5)  # 7600
                self.serial.write(need_send_data[3072:4096].encode('utf-8'))
                time.sleep(0.5)  # 7600
                self.serial.write(need_send_data[4096:5120].encode('utf-8'))
                time.sleep(0.5)  # 7600
                self.serial.write(need_send_data[5120:6144].encode('utf-8'))
                time.sleep(0.5)  # 7600
                self.serial.write(need_send_data[6144:7168].encode('utf-8'))  #
                time.sleep(0.5)  # 7600
                self.serial.write(need_send_data[7168:8192].encode('utf-8'))  #
                time.sleep(0.5)  # 7600
                self.serial.write(need_send_data[8192:8240].encode('utf-8'))  #
                # 写数据给光源 26通道 9440
            elif self.spd_num == 26:  # 9440
                print('------channel 26--------')
                time.sleep(0.5)
                self.serial.write(need_send_data[0:1024].encode('utf-8'))
                time.sleep(0.5)  # 9440
                self.serial.write(need_send_data[1024:2048].encode('utf-8'))  # 1024 1995
                time.sleep(0.5)  # 9440
                self.serial.write(need_send_data[2048:3072].encode('utf-8'))
                time.sleep(0.5)  # 9440
                self.serial.write(need_send_data[3072:4096].encode('utf-8'))
                time.sleep(0.5)  # 9440
                self.serial.write(need_send_data[4096:5120].encode('utf-8'))
                time.sleep(0.5)  # 9440
                self.serial.write(need_send_data[5120:6144].encode('utf-8'))
                time.sleep(0.5)  # 9440
                self.serial.write(need_send_data[6144:7168].encode('utf-8'))  #
                time.sleep(0.5)  # 9440
                self.serial.write(need_send_data[7168:8192].encode('utf-8'))  #
                time.sleep(0.5)  # 9440
                self.serial.write(need_send_data[8192:9216].encode('utf-8'))  #
                time.sleep(0.5)  # 9440
                self.serial.write(need_send_data[9216:9440].encode('utf-8'))  #
            elif self.spd_num == 29:  # 9440
                print('------channel 29--------')
                if self.ui.checkBox_ch1.isChecked() == True:
                    # time.sleep(0.5)
                    self.serial.write(need_send_data[0:1024].encode('utf-8'))
                    time.sleep(0.5)  # 9440
                    self.serial.write(need_send_data[1024:2048].encode('utf-8'))  # 1024 1995
                    time.sleep(0.5)  # 9440
                    self.serial.write(need_send_data[2048:3072].encode('utf-8'))
                    time.sleep(0.5)  # 9440
                    self.serial.write(need_send_data[3072:4096].encode('utf-8'))
                    time.sleep(0.5)  # 9440
                    self.serial.write(need_send_data[4096:5120].encode('utf-8'))
                    time.sleep(0.5)  # 9440
                    self.serial.write(need_send_data[5120:6144].encode('utf-8'))
                    time.sleep(0.5)  # 9440
                    self.serial.write(need_send_data[6144:7168].encode('utf-8'))  #
                    time.sleep(0.5)  # 9440
                    self.serial.write(need_send_data[7168:8192].encode('utf-8'))  #
                    time.sleep(0.5)  # 9440
                    self.serial.write(need_send_data[8192:9216].encode('utf-8'))  #
                    time.sleep(0.5)  # 10400
                    self.serial.write(need_send_data[9216:10240].encode('utf-8'))  #
                    time.sleep(0.5)  # 10400
                    self.serial.write(need_send_data[10240:10400].encode('utf-8'))  #
                if self.ui.checkBox_ch2.isChecked() == True:
                    # time.sleep(0.5)
                    self.serial_2.write(need_send_data[0:1024].encode('utf-8'))
                    time.sleep(0.5)  # 9440
                    self.serial_2.write(need_send_data[1024:2048].encode('utf-8'))  # 1024 1995
                    time.sleep(0.5)  # 9440
                    self.serial_2.write(need_send_data[2048:3072].encode('utf-8'))
                    time.sleep(0.5)  # 9440
                    self.serial_2.write(need_send_data[3072:4096].encode('utf-8'))
                    time.sleep(0.5)  # 9440
                    self.serial_2.write(need_send_data[4096:5120].encode('utf-8'))
                    time.sleep(0.5)  # 9440
                    self.serial_2.write(need_send_data[5120:6144].encode('utf-8'))
                    time.sleep(0.5)  # 9440
                    self.serial_2.write(need_send_data[6144:7168].encode('utf-8'))  #
                    time.sleep(0.5)  # 9440
                    self.serial_2.write(need_send_data[7168:8192].encode('utf-8'))  #
                    time.sleep(0.5)  # 9440
                    self.serial_2.write(need_send_data[8192:9216].encode('utf-8'))  #
                    time.sleep(0.5)  # 10400
                    self.serial_2.write(need_send_data[9216:10240].encode('utf-8'))  #
                    time.sleep(0.5)  # 10400
                    self.serial_2.write(need_send_data[10240:10400].encode('utf-8'))  #
                if self.ui.checkBox_ch3.isChecked() == True:
                    # time.sleep(0.5)
                    self.serial_3.write(need_send_data[0:1024].encode('utf-8'))
                    time.sleep(0.5)  # 9440
                    self.serial_3.write(need_send_data[1024:2048].encode('utf-8'))  # 1024 1995
                    time.sleep(0.5)  # 9440
                    self.serial_3.write(need_send_data[2048:3072].encode('utf-8'))
                    time.sleep(0.5)  # 9440
                    self.serial_3.write(need_send_data[3072:4096].encode('utf-8'))
                    time.sleep(0.5)  # 9440
                    self.serial_3.write(need_send_data[4096:5120].encode('utf-8'))
                    time.sleep(0.5)  # 9440
                    self.serial_3.write(need_send_data[5120:6144].encode('utf-8'))
                    time.sleep(0.5)  # 9440
                    self.serial_3.write(need_send_data[6144:7168].encode('utf-8'))  #
                    time.sleep(0.5)  # 9440
                    self.serial_3.write(need_send_data[7168:8192].encode('utf-8'))  #
                    time.sleep(0.5)  # 9440
                    self.serial_3.write(need_send_data[8192:9216].encode('utf-8'))  #
                    time.sleep(0.5)  # 10400
                    self.serial_3.write(need_send_data[9216:10240].encode('utf-8'))  #
                    time.sleep(0.5)  # 10400
                    self.serial_3.write(need_send_data[10240:10400].encode('utf-8'))  #
                if self.ui.checkBox_ch4.isChecked() == True:
                    time.sleep(0.5)
                    self.serial_4.write(need_send_data[0:1024].encode('utf-8'))
                    time.sleep(0.5)  # 9440
                    self.serial_4.write(need_send_data[1024:2048].encode('utf-8'))  # 1024 1995
                    time.sleep(0.5)  # 9440
                    self.serial_4.write(need_send_data[2048:3072].encode('utf-8'))
                    time.sleep(0.5)  # 9440
                    self.serial_4.write(need_send_data[3072:4096].encode('utf-8'))
                    time.sleep(0.5)  # 9440
                    self.serial_4.write(need_send_data[4096:5120].encode('utf-8'))
                    time.sleep(0.5)  # 9440
                    self.serial_4.write(need_send_data[5120:6144].encode('utf-8'))
                    time.sleep(0.5)  # 9440
                    self.serial_4.write(need_send_data[6144:7168].encode('utf-8'))  #
                    time.sleep(0.5)  # 9440
                    self.serial_4.write(need_send_data[7168:8192].encode('utf-8'))  #
                    time.sleep(0.5)  # 9440
                    self.serial_4.write(need_send_data[8192:9216].encode('utf-8'))  #
                    time.sleep(0.5)  # 10400
                    self.serial_4.write(need_send_data[9216:10240].encode('utf-8'))  #
                    time.sleep(0.5)  # 10400
                    self.serial_4.write(need_send_data[10240:10400].encode('utf-8'))  #
                if self.ui.checkBox_ch5.isChecked() == True:
                    # time.sleep(0.5)
                    self.serial_5.write(need_send_data[0:1024].encode('utf-8'))
                    time.sleep(0.5)  # 9440
                    self.serial_5.write(need_send_data[1024:2048].encode('utf-8'))  # 1024 1995
                    time.sleep(0.5)  # 9440
                    self.serial_5.write(need_send_data[2048:3072].encode('utf-8'))
                    time.sleep(0.5)  # 9440
                    self.serial_5.write(need_send_data[3072:4096].encode('utf-8'))
                    time.sleep(0.5)  # 9440
                    self.serial_5.write(need_send_data[4096:5120].encode('utf-8'))
                    time.sleep(0.5)  # 9440
                    self.serial_5.write(need_send_data[5120:6144].encode('utf-8'))
                    time.sleep(0.5)  # 9440
                    self.serial_5.write(need_send_data[6144:7168].encode('utf-8'))  #
                    time.sleep(0.5)  # 9440
                    self.serial_5.write(need_send_data[7168:8192].encode('utf-8'))  #
                    time.sleep(0.5)  # 9440
                    self.serial_5.write(need_send_data[8192:9216].encode('utf-8'))  #
                    time.sleep(0.5)  # 10400
                    self.serial_5.write(need_send_data[9216:10240].encode('utf-8'))  #
                    time.sleep(0.5)  # 10400
                    self.serial_5.write(need_send_data[10240:10400].encode('utf-8'))  #
                if self.ui.checkBox_ch6.isChecked() == True:
                    # time.sleep(0.5)
                    self.checkBox_ch6.write(need_send_data[0:1024].encode('utf-8'))
                    time.sleep(0.5)  # 9440
                    self.checkBox_ch6.write(need_send_data[1024:2048].encode('utf-8'))  # 1024 1995
                    time.sleep(0.5)  # 9440
                    self.checkBox_ch6.write(need_send_data[2048:3072].encode('utf-8'))
                    time.sleep(0.5)  # 9440
                    self.checkBox_ch6.write(need_send_data[3072:4096].encode('utf-8'))
                    time.sleep(0.5)  # 9440
                    self.checkBox_ch6.write(need_send_data[4096:5120].encode('utf-8'))
                    time.sleep(0.5)  # 9440
                    self.checkBox_ch6.write(need_send_data[5120:6144].encode('utf-8'))
                    time.sleep(0.5)  # 9440
                    self.checkBox_ch6.write(need_send_data[6144:7168].encode('utf-8'))  #
                    time.sleep(0.5)  # 9440
                    self.checkBox_ch6.write(need_send_data[7168:8192].encode('utf-8'))  #
                    time.sleep(0.5)  # 9440
                    self.checkBox_ch6.write(need_send_data[8192:9216].encode('utf-8'))  #
                    time.sleep(0.5)  # 10400
                    self.checkBox_ch6.write(need_send_data[9216:10240].encode('utf-8'))  #
                    time.sleep(0.5)  # 10400
                    self.checkBox_ch6.write(need_send_data[10240:10400].encode('utf-8'))  #

            # 第二台
            '''
            start_command = "52 58 45 0d 0a"
            self.serial.write(bytes.fromhex(start_command))
            time.sleep(0.5)
            print('len:', len(need_send_data))
            data_lens = "43 4f 4e" + write_data.hex_data(len(need_send_data))
            self.serial.write(bytes.fromhex(data_lens))
            time.sleep(0.5)
            self.serial.write(need_send_data[0:1024].encode('utf-8'))
            time.sleep(0.5)  # 7600
            self.serial.write(need_send_data[1024:2048].encode('utf-8'))  # 1024 1995
            time.sleep(0.5)  # 7600
            self.serial.write(need_send_data[2048:3072].encode('utf-8'))
            time.sleep(0.5)  # 7600
            self.serial.write(need_send_data[3072:4096].encode('utf-8'))
            time.sleep(0.5)  # 7600
            self.serial.write(need_send_data[4096:5120].encode('utf-8'))
            time.sleep(0.5)  # 7600
            self.serial.write(need_send_data[5120:6144].encode('utf-8'))
            time.sleep(0.5)  # 7600
            self.serial.write(need_send_data[6144:6320].encode('utf-8'))
            time.sleep(0.5)  # 7600
            '''
            self.ui.textBrowser_message.append(
                "\n[INFO] %s \n写入光源数据完成!" % (time.strftime("%Y-%m-%d %H:%M:%S", time.gmtime())))
            self.ui.textBrowser_message.moveCursor(self.ui.textBrowser_message.textCursor().End)

        self.ui.pushButton_saveOrder_to_light.clicked.connect(saveOrder_to_light_click)

    def _async_raise(self, tid, exctype):
        """raises the exception, performs cleanup if needed"""
        tid = ctypes.c_long(tid)
        if not inspect.isclass(exctype):
            exctype = type(exctype)
        res = ctypes.pythonapi.PyThreadState_SetAsyncExc(tid, ctypes.py_object(exctype))
        if res == 0:
            raise ValueError("invalid thread id")
        elif res != 1:
            # """if it returns a number greater than one, you're in trouble,
            # and you should call it again with exc=NULL to revert the effect"""
            ctypes.pythonapi.PyThreadState_SetAsyncExc(tid, None)
            raise SystemError("PyThreadState_SetAsyncExc failed")

    def stop_thread(self, thread):
        self._async_raise(thread.ident, SystemExit)


if __name__ == '__main__':
    # QApplication.setHighDpiScaleFactorRoundingPolicy(Qt.HighDpiScaleFactorRoundingPolicy.PassThrough)
    # QApplication.setAttribute(Qt.AA_EnableHighDpiScaling)
    # app = QApplication(sys.argv)
    # QApplication.setHighDpiScaleFactorRoundingPolicy(Qt.HighDpiScaleFactorRoundingPolicy.PassThrough)
    # QApplication.setAttribute(Qt.AA_EnableHighDpiScaling)
    # QtWidgets.QApplication.setAttribute(QtCore.Qt.AA_EnableHighDpiScaling, True)
    # QtWidgets.QApplication.setAttribute(QtCore.Qt.AA_UseHighDpiPixmaps, True)

    app = QApplication([])
    stats = Stats()
    stats.ui.showMaximized()
    app.exec_()
    me = os.getpid()
    kill_proc_tree(me)
