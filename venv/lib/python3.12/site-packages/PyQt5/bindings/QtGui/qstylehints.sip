// qstylehints.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QStyleHints : public QObject
{
%TypeHeaderCode
#include <qstylehints.h>
%End

public:
    int mouseDoubleClickInterval() const;
    int startDragDistance() const;
    int startDragTime() const;
    int startDragVelocity() const;
    int keyboardInputInterval() const;
    int keyboardAutoRepeatRate() const;
    int cursorFlashTime() const;
    bool showIsFullScreen() const;
    int passwordMaskDelay() const;
    qreal fontSmoothingGamma() const;
    bool useRtlExtensions() const;
%If (Qt_5_1_0 -)
    QChar passwordMaskCharacter() const;
%End
%If (Qt_5_2_0 -)
    bool setFocusOnTouchRelease() const;
%End
%If (Qt_5_3_0 -)
    int mousePressAndHoldInterval() const;
%End
%If (Qt_5_5_0 -)
    Qt::TabFocusBehavior tabFocusBehavior() const;
%End
%If (Qt_5_5_0 -)
    bool singleClickActivation() const;
%End

signals:
%If (Qt_5_5_0 -)
    void cursorFlashTimeChanged(int cursorFlashTime);
%End
%If (Qt_5_5_0 -)
    void keyboardInputIntervalChanged(int keyboardInputInterval);
%End
%If (Qt_5_5_0 -)
    void mouseDoubleClickIntervalChanged(int mouseDoubleClickInterval);
%End
%If (Qt_5_5_0 -)
    void startDragDistanceChanged(int startDragDistance);
%End
%If (Qt_5_5_0 -)
    void startDragTimeChanged(int startDragTime);
%End
%If (Qt_5_7_0 -)
    void mousePressAndHoldIntervalChanged(int mousePressAndHoldInterval);
%End
%If (Qt_5_7_0 -)
    void tabFocusBehaviorChanged(Qt::TabFocusBehavior tabFocusBehavior);
%End

public:
%If (Qt_5_6_0 -)
    bool showIsMaximized() const;
%End
%If (Qt_5_8_0 -)
    bool useHoverEffects() const;
%End
%If (Qt_5_8_0 -)
    void setUseHoverEffects(bool useHoverEffects);
%End

signals:
%If (Qt_5_8_0 -)
    void useHoverEffectsChanged(bool useHoverEffects);
%End

public:
%If (Qt_5_9_0 -)
    int wheelScrollLines() const;
%End

signals:
%If (Qt_5_9_0 -)
    void wheelScrollLinesChanged(int scrollLines);
%End

public:
%If (Qt_5_10_0 -)
    bool showShortcutsInContextMenus() const;
%End
%If (Qt_5_11_0 -)
    int mouseQuickSelectionThreshold() const;
%End

signals:
%If (Qt_5_11_0 -)
    void mouseQuickSelectionThresholdChanged(int threshold);
%End

public:
%If (Qt_5_13_0 -)
    void setShowShortcutsInContextMenus(bool showShortcutsInContextMenus);
%End

signals:
%If (Qt_5_13_0 -)
    void showShortcutsInContextMenusChanged(bool);
%End

public:
%If (Qt_5_14_0 -)
    int mouseDoubleClickDistance() const;
%End
%If (Qt_5_14_0 -)
    int touchDoubleTapDistance() const;
%End

private:
    QStyleHints();
};
