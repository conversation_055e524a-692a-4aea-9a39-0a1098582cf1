// qpainterpath.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QPainterPath
{
%TypeHeaderCode
#include <qpainterpath.h>
%End

public:
    enum ElementType
    {
        MoveToElement,
        LineToElement,
        CurveToElement,
        CurveToDataElement,
    };

    class Element
    {
%TypeHeaderCode
#include <qpainterpath.h>
%End

    public:
        qreal x;
        qreal y;
        QPainterPath::ElementType type;
        bool isMoveTo() const;
        bool isLineTo() const;
        bool isCurveTo() const;
        bool operator==(const QPainterPath::Element &e) const;
        bool operator!=(const QPainterPath::Element &e) const;
        operator QPointF() const;
    };

    QPainterPath();
    explicit QPainterPath(const QPointF &startPoint);
    QPainterPath(const QPainterPath &other);
    ~QPainterPath();
    void closeSubpath();
    void moveTo(const QPointF &p);
    void lineTo(const QPointF &p);
    void arcTo(const QRectF &rect, qreal startAngle, qreal arcLength);
    void cubicTo(const QPointF &ctrlPt1, const QPointF &ctrlPt2, const QPointF &endPt);
    void quadTo(const QPointF &ctrlPt, const QPointF &endPt);
    QPointF currentPosition() const;
    void addRect(const QRectF &rect);
    void addEllipse(const QRectF &rect);
    void addPolygon(const QPolygonF &polygon);
    void addText(const QPointF &point, const QFont &f, const QString &text);
    void addPath(const QPainterPath &path);
    void addRegion(const QRegion &region);
    void connectPath(const QPainterPath &path);
    bool contains(const QPointF &pt) const;
    bool contains(const QRectF &rect) const;
    bool intersects(const QRectF &rect) const;
    QRectF boundingRect() const;
    QRectF controlPointRect() const;
    Qt::FillRule fillRule() const;
    void setFillRule(Qt::FillRule fillRule);
    QPainterPath toReversed() const;
    QList<QPolygonF> toSubpathPolygons() const;
%MethodCode
        sipRes = new QList<QPolygonF>(sipCpp->toSubpathPolygons());
%End

    QList<QPolygonF> toFillPolygons() const;
%MethodCode
        sipRes = new QList<QPolygonF>(sipCpp->toFillPolygons());
%End

    QPolygonF toFillPolygon() const;
%MethodCode
        sipRes = new QPolygonF(sipCpp->toFillPolygon());
%End

    bool operator==(const QPainterPath &other) const;
    bool operator!=(const QPainterPath &other) const;
    void moveTo(qreal x, qreal y);
    void arcMoveTo(const QRectF &rect, qreal angle);
    void arcMoveTo(qreal x, qreal y, qreal w, qreal h, qreal angle);
    void arcTo(qreal x, qreal y, qreal w, qreal h, qreal startAngle, qreal arcLenght);
    void lineTo(qreal x, qreal y);
    void cubicTo(qreal ctrlPt1x, qreal ctrlPt1y, qreal ctrlPt2x, qreal ctrlPt2y, qreal endPtx, qreal endPty);
    void quadTo(qreal ctrlPtx, qreal ctrlPty, qreal endPtx, qreal endPty);
    void addEllipse(qreal x, qreal y, qreal w, qreal h);
    void addRect(qreal x, qreal y, qreal w, qreal h);
    void addText(qreal x, qreal y, const QFont &f, const QString &text);
    bool isEmpty() const;
    int elementCount() const;
    QPainterPath::Element elementAt(int i) const;
    void setElementPositionAt(int i, qreal x, qreal y);
    QList<QPolygonF> toSubpathPolygons(const QTransform &matrix) const;
    QList<QPolygonF> toFillPolygons(const QTransform &matrix) const;
    QPolygonF toFillPolygon(const QTransform &matrix) const;
    qreal length() const;
    qreal percentAtLength(qreal t) const;
    QPointF pointAtPercent(qreal t) const;
    qreal angleAtPercent(qreal t) const;
    qreal slopeAtPercent(qreal t) const;
    bool intersects(const QPainterPath &p) const;
    bool contains(const QPainterPath &p) const;
    QPainterPath united(const QPainterPath &r) const;
    QPainterPath intersected(const QPainterPath &r) const;
    QPainterPath subtracted(const QPainterPath &r) const;
    void addRoundedRect(const QRectF &rect, qreal xRadius, qreal yRadius, Qt::SizeMode mode = Qt::AbsoluteSize);
    void addRoundedRect(qreal x, qreal y, qreal w, qreal h, qreal xRadius, qreal yRadius, Qt::SizeMode mode = Qt::AbsoluteSize);
    void addEllipse(const QPointF &center, qreal rx, qreal ry);
    QPainterPath simplified() const;
    QPainterPath operator&(const QPainterPath &other) const;
    QPainterPath operator|(const QPainterPath &other) const;
    QPainterPath operator+(const QPainterPath &other) const;
    QPainterPath operator-(const QPainterPath &other) const;
    QPainterPath &operator&=(const QPainterPath &other);
    QPainterPath &operator|=(const QPainterPath &other);
    QPainterPath &operator+=(const QPainterPath &other);
    QPainterPath &operator-=(const QPainterPath &other);
    void translate(qreal dx, qreal dy);
    QPainterPath translated(qreal dx, qreal dy) const;
    void translate(const QPointF &offset);
    QPainterPath translated(const QPointF &offset) const;
    void swap(QPainterPath &other /Constrained/);
%If (Qt_5_13_0 -)
    void clear();
%End
%If (Qt_5_13_0 -)
    void reserve(int size);
%End
%If (Qt_5_13_0 -)
    int capacity() const;
%End
};

QDataStream &operator<<(QDataStream &, const QPainterPath & /Constrained/) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &, QPainterPath & /Constrained/) /ReleaseGIL/;

class QPainterPathStroker
{
%TypeHeaderCode
#include <qpainterpath.h>
%End

public:
    QPainterPathStroker();
%If (Qt_5_3_0 -)
    explicit QPainterPathStroker(const QPen &pen);
%End
    ~QPainterPathStroker();
    void setWidth(qreal width);
    qreal width() const;
    void setCapStyle(Qt::PenCapStyle style);
    Qt::PenCapStyle capStyle() const;
    void setJoinStyle(Qt::PenJoinStyle style);
    Qt::PenJoinStyle joinStyle() const;
    void setMiterLimit(qreal length);
    qreal miterLimit() const;
    void setCurveThreshold(qreal threshold);
    qreal curveThreshold() const;
    void setDashPattern(Qt::PenStyle);
    void setDashPattern(const QVector<qreal> &dashPattern);
    QVector<qreal> dashPattern() const;
    QPainterPath createStroke(const QPainterPath &path) const;
    void setDashOffset(qreal offset);
    qreal dashOffset() const;

private:
    QPainterPathStroker(const QPainterPathStroker &);
};
