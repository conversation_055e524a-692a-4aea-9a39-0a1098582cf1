// qsplitter.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QSplitter : public QFrame
{
%TypeHeaderCode
#include <qsplitter.h>
%End

public:
    explicit QSplitter(QWidget *parent /TransferThis/ = 0);
    QSplitter(Qt::Orientation orientation, QWidget *parent /TransferThis/ = 0);
    virtual ~QSplitter();
    void addWidget(QWidget *widget /Transfer/);
    void insertWidget(int index, QWidget *widget /Transfer/);
    void setOrientation(Qt::Orientation);
    Qt::Orientation orientation() const;
    void setChildrenCollapsible(bool);
    bool childrenCollapsible() const;
    void setCollapsible(int index, bool);
    bool isCollapsible(int index) const;
    void setOpaqueResize(bool opaque = true);
    bool opaqueResize() const;
    void refresh();
    virtual QSize sizeHint() const;
    virtual QSize minimumSizeHint() const;
    QList<int> sizes() const;
    void setSizes(const QList<int> &list);
    QByteArray saveState() const;
    bool restoreState(const QByteArray &state);
    int handleWidth() const;
    void setHandleWidth(int);
    int indexOf(QWidget *w) const;
    QWidget *widget(int index) const;
    int count() const /__len__/;
    void getRange(int index, int *, int *) const;
    QSplitterHandle *handle(int index) const /Transfer/;
    void setStretchFactor(int index, int stretch);
%If (Qt_5_9_0 -)
    QWidget *replaceWidget(int index, QWidget *widget /Transfer/) /TransferBack/;
%End

signals:
    void splitterMoved(int pos, int index);

protected:
    virtual QSplitterHandle *createHandle() /Transfer/;
    virtual void childEvent(QChildEvent *);
    virtual bool event(QEvent *);
    virtual void resizeEvent(QResizeEvent *);
    virtual void changeEvent(QEvent *);
    void moveSplitter(int pos, int index);
    void setRubberBand(int position);
    int closestLegalPosition(int, int);
};

class QSplitterHandle : public QWidget
{
%TypeHeaderCode
#include <qsplitter.h>
%End

public:
    QSplitterHandle(Qt::Orientation o, QSplitter *parent /TransferThis/);
    virtual ~QSplitterHandle();
    void setOrientation(Qt::Orientation o);
    Qt::Orientation orientation() const;
    bool opaqueResize() const;
    QSplitter *splitter() const;
    virtual QSize sizeHint() const;

protected:
    virtual void paintEvent(QPaintEvent *);
    virtual void mouseMoveEvent(QMouseEvent *);
    virtual void mousePressEvent(QMouseEvent *);
    virtual void mouseReleaseEvent(QMouseEvent *);
    virtual bool event(QEvent *);
    void moveSplitter(int p);
    int closestLegalPosition(int p);
    virtual void resizeEvent(QResizeEvent *);
};
