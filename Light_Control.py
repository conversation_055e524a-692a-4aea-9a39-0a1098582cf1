# _*_ coding:utf-8 _*_
# Group  :   ColorSpace
# Author :   <PERSON><PERSON><PERSON><PERSON>
# Data   :   2021/06/25
# File   :   Rotate_Light_Control.py
# Tool   :   PyCharm


import time
import serial
import json
import os
import socket


# 光源个数确定
'''
A: 4
H: 12个灯杯
U30: 4个
TL84: 4个
CWF: 4个
D50: 8个
D65: 12个
D75: 10个
LED: 无
'''


# LED 照度级数 0-2000
# TL83 0-1000
# CWF 0-1000


port_num = "com27"


def dec2bin(string_num):
    num = int(string_num)
    mid = []
    while True:
        if num == 0: break
        num,rem = divmod(num, 2)
        mid.append(base[rem])

    return ''.join([str(x) for x in mid[::-1]])


# global definition
# base = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, A, B, C, D, E, F]
base = [str(x) for x in range(10)] + [ chr(x) for x in range(ord('A'),ord('A')+6)]


# bin2dec
# 二进制 to 十进制: int(str,n=10)
def bin2dec(string_num):
    return str(int(string_num, 2))


# dec2bin
# 十进制 to 二进制: bin()
def dec2bin(string_num):
    num = int(string_num)
    mid = []
    while True:
        if num == 0: break
    num, rem = divmod(num, 2)
    mid.append(base[rem])
    print(''.join([str(x) for x in mid[::-1]]))
    return ''.join([str(x) for x in mid[::-1]])


# hex2dec
# 十六进制 to 十进制
def hex2dec(string_num):
    return str(int(string_num.upper(), 16))


def hex2bin(string_num):
    print(dec2bin(hex2dec(string_num.upper())))
    return dec2bin(hex2dec(string_num.upper()))


def bin2hex(string_num):
    print(dec2hex(bin2dec(string_num)))
    return dec2hex(bin2dec(string_num))


base = [str(x) for x in range(10)] + [chr(x) for x in range(ord('A'), ord('A') + 6)]

def dec2hex(num):
    l = []
    if num < 0:
        return '-' + dec2hex(abs(num))
    while True:
        num, rem = divmod(num, 16)
        l.append(base[rem])
        if num == 0:
            return ''.join(l[::-1])


def hex_data(data):
    speed_data = ""
    if len(dec2hex(int(data))) == 1:
        speed_data = "0000000" + dec2hex(int(data))
    elif len(dec2hex(int(data))) == 2:
        speed_data = "000000" + dec2hex(int(data))
    elif len(dec2hex(int(int(data)))) == 3:
        speed_data = "00000" + dec2hex(int(int(data)))
    elif len(dec2hex(int(int(data)))) == 4:
        speed_data = "0000" + dec2hex(int(int(data)))
    elif len(dec2hex(int(data))) == 5:
        speed_data = "000" + dec2hex(int(data))
    elif len(dec2hex(int(data))) == 6:
        speed_data = "00" + dec2hex(int(data))
    elif len(dec2hex(int(data))) == 7:
        speed_data = "0" + dec2hex(int(data))
    elif len(dec2hex(int(data))) == 8:
        speed_data = dec2hex(int(data))
    result_data = speed_data[0:4] + speed_data[4:8]
    return result_data


def hex_data_4(data):
    speed_data = ""
    if len(dec2hex(int(int(data)))) == 4:
        speed_data = dec2hex(int(int(data)))
    elif len(dec2hex(int(data))) == 3:
        speed_data = "0" + dec2hex(int(data))
    elif len(dec2hex(int(data))) == 2:
        speed_data = "00" + dec2hex(int(data))
    elif len(dec2hex(int(data))) == 1:
        speed_data = "000" + dec2hex(int(data))
    result_data =  speed_data[0:2] + ' ' + speed_data[2:4]
    #print("result_data:",result_data)
    return result_data


def hex_data_41(data):
    speed_data = ""
    if len(dec2hex(int(int(data)))) == 4:
        speed_data = dec2hex(int(int(data)))
    elif len(dec2hex(int(data))) == 3:
        speed_data = "0" + dec2hex(int(data))
    elif len(dec2hex(int(data))) == 2:
        speed_data = "00" + dec2hex(int(data))
    elif len(dec2hex(int(data))) == 1:
        speed_data = "000" + dec2hex(int(data))
    result_data =  speed_data[0:2] + speed_data[2:4]
    #print("result_data:",result_data)
    return result_data


def hex_data_2(data):
    speed_data = ""
    if len(dec2hex(int(int(data)))) == 2:
        speed_data = dec2hex(int(int(data)))
    elif len(dec2hex(int(data))) == 1:
        speed_data = "0" + dec2hex(int(data))
    return speed_data


def add_check_code(input_hex_data):
    data = bytes.fromhex(input_hex_data) # 转成10进制 16
    # data = bytes.fromhex("AA 00 00 00 ff ff ff ff 00 00 00")
    # print(sum(data))
    result_data = bin(int(sum(data))).replace("0b", "")
   # print(result_data)
    result_list = []
    len_num = len(result_data)
    # print("len_num:",len_num)
    # print(type(result_data[0:1]))
    for i in range(1, len_num + 1):
        #  print(result_data[i-1:i])
        if result_data[i - 1:i] == "0":
            result_list.append(1)
        elif result_data[i - 1:i] == "1":
            result_list.append(0)
    #print("result_list:",result_list)
    hex_data = str(result_list).replace(",", "").replace("[", "").replace("]", "").replace(" ", "")
    # print("data:",hex_data)
    #print("len:",len(hex_data))
    only_hexdata = hex_data[len(hex_data) - 8:len(hex_data)]
    #print("only_hexdata",only_hexdata)
    if len(hex(int(only_hexdata,2)+1).replace("0x","")) == 1:
        data = "0" + hex(int(only_hexdata,2)+1).replace("0x","")
    else:
        data_len = len(hex(int(only_hexdata, 2) + 1).replace("0x", ""))  # 011
        data = hex(int(only_hexdata, 2) + 1).replace("0x", "")[data_len - 2:data_len]
    #print("data",data)
    #print("send_data:",input_hex_data +  str(data))
    return input_hex_data +  str(data)


def disconnect_light():
    light_ser.close()


def read_status():
    pass


def open_tl83_cwf(light_name,light_group,light_num):
    pass
    if len(hex(int(light_num, 2)).replace("0x", "")) == 1:
        data = "0" + hex(int(light_num, 2)).replace("0x", "")
    else:
        data = hex(int(light_num, 2)).replace("0x", "")
        print("data:",data)
    if light_name == "TL83":
        send_data = "AA" + hex_data_2(light_group) + "00 0B 01 06 " + data+ "00 00 00"
        light_ser.write(bytes.fromhex(add_check_code(send_data)))
    if light_name == "TL84":
        send_data = "AA" + hex_data_2(light_group) + "00 0B 01 07 " + data+ "00 00 00"
        light_ser.write(bytes.fromhex(add_check_code(send_data)))
    elif light_name == "CWF":
        send_data = "AA" + hex_data_2(light_group) + "00 0B 01 08 " + data + " 00 00 00"
        light_ser.write(bytes.fromhex(add_check_code(send_data)))
    time.sleep(0.2)


def close_light(light_name,light_group):
    if light_name == "LED":
        send_data = "AA" + hex_data_2(light_group) + "00 0B 01 02 00 00 00 00"
        light_ser.write(bytes.fromhex(add_check_code(send_data)))
        # send_data = "AA 01 00 0B 01 02 00 00 00 00 48"
        # light_ser.write(bytes.fromhex(send_data))
    elif light_name == "TL83":
        send_data = "AA" + hex_data_2(light_group) + "00 0B 01 06 00 00 00 00"
        light_ser.write(bytes.fromhex(add_check_code(send_data)))

    elif light_name == "TL84":
        send_data = "AA" + hex_data_2(light_group) + "00 0B 01 07 00 00 00 00"
        light_ser.write(bytes.fromhex(add_check_code(send_data)))
        # # send_data = "AA 01 00 0B 01 07 00 00 00 00 43"
        # light_ser.write(bytes.fromhex(send_data))
    elif light_name == "CWF":
        send_data = "AA" + hex_data_2(light_group) + "00 0B 01 08 00 00 00 00"
        light_ser.write(bytes.fromhex(add_check_code(send_data)))
        # # send_data = "AA 01 00 0B 01 08 00 00 00 00 42"
        # light_ser.write(bytes.fromhex(send_data))


def set_light_param(light_name,light_group,illum_num):
    if light_name == "H":
        send_data = "AA" + hex_data_2(light_group) + "010B0101" + hex_data_4(illum_num) + "0000"
        light_ser.write(bytes.fromhex(add_check_code(send_data)))
    elif light_name == "A":
        send_data = "AA" + hex_data_2(light_group)+ "010B0102" + hex_data_4(illum_num) + "0000"
        light_ser.write(bytes.fromhex(add_check_code(send_data)))
    elif light_name =="D50":
        send_data = "AA" + hex_data_2(light_group)+ "010B0103" + hex_data_4(illum_num) + "0000"
        light_ser.write(bytes.fromhex(add_check_code(send_data)))
    elif light_name == "D65":
        send_data = "AA" + hex_data_2(light_group)+ "010B0104" + hex_data_4(illum_num) + "0000"
        light_ser.write(bytes.fromhex(add_check_code(send_data)))
    elif light_name == "TL83":
        send_data = "AA" + hex_data_2(light_group) + "010B0106" + hex_data_4(illum_num) + "0000"
        light_ser.write(bytes.fromhex(add_check_code(send_data)))
    elif light_name == "TL84":
        send_data = "AA" + hex_data_2(light_group)+ "010B0107" + hex_data_4(illum_num) + "0000"
        light_ser.write(bytes.fromhex(add_check_code(send_data)))
    elif light_name == "CWF":
        send_data = "AA" + hex_data_2(light_group)+ "010B0108" + hex_data_4(illum_num) + "0000"
        light_ser.write(bytes.fromhex(add_check_code(send_data)))
    elif light_name == "4000K":
        send_data = "AA" + hex_data_2(light_group) + "010B010E" + hex_data_4(illum_num) + "0000"
        light_ser.write(bytes.fromhex(add_check_code(send_data)))
    time.sleep(0.2)




'''
新版荧光灯控制
# _*_ coding:utf-8 _*_
# Group  :   ColorSpace
# Author :   Chengyinhui
# Data   :   2023/02/13
# File   :   Fluorescent lamp control_api.py
# Tool   :   PyCharm
'''


def connect_light(com_number):  # 连接光源
    try:
        global light_ser
        light_ser = serial.Serial(com_number, 115200, bytesize=8, parity='N', stopbits=1, timeout=0.1)  # 230400
        light_ser.close()
        light_ser.open()
    except Exception as e:
        print("---异常---：", e)


def send_data_to_light(param):
    try:
        light_ser.write(bytes.fromhex(param))
        time.sleep(0.3)
        data = light_ser.readall()
        print('recv------:', data.hex())
        return data
    except Exception as e:
        print("---异常---：", e)

    # if data ==b'\xa3j\xa6\x01':
    #     print(True)
    # else:
    #     print(False)
    # #print('------:',data)
    # time.sleep(0.3)


def switch_light(light_name,light_switch):
    if light_name == "CWF":
        if light_switch =='ON':
            send_data = "6A A6 00 C1 01"
            send_data_to_light(send_data)
        elif light_switch == 'OFF':
            send_data = "6A A6 00 C1 00"
            send_data_to_light(send_data)

    elif light_name == "TL84":
        if light_switch == 'ON':
            send_data = "6A A6 00 B1 01"
            send_data_to_light(send_data)

        elif light_switch == 'OFF':
            send_data = "6A A6 00 B1 00"
            send_data_to_light(send_data)

    elif light_name == "TL83":
        if light_switch == 'ON':
            send_data = "6A A6 00 A1 01"
            send_data_to_light(send_data)

        elif light_switch == 'OFF':
            send_data = "6A A6 00 A1 00"
            send_data_to_light(send_data)


def choose_light_num(light_name,light_group,num):
    if light_name == 'TL84':
        if light_group[0:1] == '0':
            send_data = "6A A6 00 06 00"
            send_data_to_light(send_data)
        elif light_group[0:1] == '1':
            send_data = "6A A6 00 06 01"
            send_data_to_light(send_data)

        if light_group[1:2] == '0':
            send_data = "6A A6 00 07 00"
            send_data_to_light(send_data)

        elif light_group[1:2] == '1':
            send_data = "6A A6 00 07 01"
            send_data_to_light(send_data)

        if light_group[2:3] == '0':
            send_data = "6A A6 00 08 00"
            send_data_to_light(send_data)
        elif light_group[2:3] == '1':
            send_data = "6A A6 00 08 01"
            send_data_to_light(send_data)
        if light_group[3:4] == '0':
            send_data = "6A A6 00 09 00"
            send_data_to_light(send_data)

        elif light_group[3:4] == '1':
            send_data = "6A A6 00 09 01"
            send_data_to_light(send_data)

        if light_group[4:5] == '0':
            send_data = "6A A6 00 0a 00"
            send_data_to_light(send_data)
        elif light_group[4:5] == '1':
            send_data = "6A A6 00 0a 01"
            send_data_to_light(send_data)
        send_data = '6A A6 0B '+ hex_data_4(num)
        print('send_data:',send_data)
        send_data_to_light(send_data)

    elif light_name == 'TL83':
        if light_group[0:1] == '0':
            send_data = "6A A6 00 01 00"
            send_data_to_light(send_data)
        elif light_group[0:1] == '1':
            send_data = "6A A6 00 01 01"
            send_data_to_light(send_data)

        if light_group[1:2] == '0':
            send_data = "6A A6 00 02 00"
            send_data_to_light(send_data)

        elif light_group[1:2] == '1':
            send_data = "6A A6 00 02 01"
            send_data_to_light(send_data)

        if light_group[2:3] == '0':
            send_data = "6A A6 00 03 00"
            send_data_to_light(send_data)
        elif light_group[2:3] == '1':
            send_data = "6A A6 00 03 01"
            send_data_to_light(send_data)
        if light_group[3:4] == '0':
            send_data = "6A A6 00 04 00"
            send_data_to_light(send_data)

        elif light_group[3:4] == '1':
            send_data = "6A A6 00 04 01"
            send_data_to_light(send_data)

        if light_group[4:5] == '0':
            send_data = "6A A6 00 05 00"
            send_data_to_light(send_data)
        elif light_group[4:5] == '1':
            send_data = "6A A6 00 05 01"
            send_data_to_light(send_data)
        send_data = '6A A6 0A '+ hex_data_4(num)
        print('send_data:',send_data)
        send_data_to_light(send_data)

        pass
    elif light_name == 'TL84':
        if light_group[0:1] == '0':
            send_data = "6A A6 00 0b 00"
            send_data_to_light(send_data)
        elif light_group[0:1] == '1':
            send_data = "6A A6 00 0b 01"
            send_data_to_light(send_data)

        if light_group[1:2] == '0':
            send_data = "6A A6 00 0c 00"
            send_data_to_light(send_data)

        elif light_group[1:2] == '1':
            send_data = "6A A6 00 0c 01"
            send_data_to_light(send_data)

        if light_group[2:3] == '0':
            send_data = "6A A6 00 0d 00"
            send_data_to_light(send_data)
        elif light_group[2:3] == '1':
            send_data = "6A A6 00 0d 01"
            send_data_to_light(send_data)
        if light_group[3:4] == '0':
            send_data = "6A A6 00 0e 00"
            send_data_to_light(send_data)

        elif light_group[3:4] == '1':
            send_data = "6A A6 00 0e 01"
            send_data_to_light(send_data)

        if light_group[4:5] == '0':
            send_data = "6A A6 00 0f 00"
            send_data_to_light(send_data)
        elif light_group[4:5] == '1':
            send_data = "6A A6 00 0f 01"
            send_data_to_light(send_data)
        send_data = '6A A6 0C '+ hex_data_4(num)
        print('send_data:',send_data)
        send_data_to_light(send_data)


def set_qc_light(num):
    if num == 1:
        send_data = "01 00 00 00 AA 00 6E 00 64 00 CD 00 84 00 DD 00 E1 00 CA 00 49 00 27 00 73 00 76 00 A3 00 FF 00 F8 00 00 00 69 00 75 00 14 29 0d 0d 0a"
        send_data_to_light(send_data)
    elif num ==2:
        send_data = "01 00 00 00 AD 00 78 00 64 00 E6 00 92 00 FD 00 FC 00 E6 00 50 00 2C 00 83 00 85 00 BC 01 22 01 1A 00 00 00 71 00 7F 00 14 29 0d 0d 0a"
        send_data_to_light(send_data)
    elif num ==3:
        send_data = "01 00 00 00 C0 00 87 00 6E 01 0A 00 A2 01 28 01 2A 01 0D 00 59 00 32 00 97 00 9A 00 DE 01 4C 01 44 00 00 00 7C 00 8D 00 15 29 0d 0d 0a"
        send_data_to_light(send_data)
    elif num == 4:
        send_data = "01 00 00 00 CD 00 8C 00 78 01 25 00 AE 01 46 01 4F 01 2C 00 60 00 34 00 A7 00 AA 00 F9 01 6E 01 65 00 00 00 84 00 97 00 15 29 0d 0d 0a"
        send_data_to_light(send_data)
    elif num ==5:
        send_data = "01 00 00 00 DA 00 8D 00 78 01 42 00 BB 01 68 01 71 01 4E 00 67 00 36 00 B8 00 BB 01 15 01 95 01 8E 00 00 00 8E 00 A0 00 15 29 0d 0d 0a"
        send_data_to_light(send_data)
    elif num == 6:
        send_data = "01 00 00 00 E9 00 A0 00 78 01 63 00 CA 01 93 01 98 01 77 00 70 00 38 00 CD 00 CF 01 38 01 BF 01 B5 00 00 00 98 00 AB 00 16 29 0d 0d 0a"
        send_data_to_light(send_data)


def close_qc_light():
    send_data = "01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 29 0d 0d 0a"
    send_data_to_light(send_data)


def set_qc_light_illum(data):
    #send_data = "01 00 00 00 E9 00 A0 00 78 01 63 00 CA 01 93 01 98 01 77 00 70 00 38 00 CD 00 CF 01 38 01 BF 01 B5 00 00 00 98 00 AB 00 16 29 0d 0d 0a"
    send_data_to_light(data)


def disconnect_light():
    try:
        light_ser.close()
    except Exception as e:
        print("---异常---：", e)


def hex_data(data):
    speed_data = ""
    if len(dec2hex(int(data))) == 1:
        speed_data = "0000000" + dec2hex(int(data))
    elif len(dec2hex(int(data))) == 2:
        speed_data = "000000" + dec2hex(int(data))
    elif len(dec2hex(int(int(data)))) == 3:
        speed_data = "00000" + dec2hex(int(int(data)))
    elif len(dec2hex(int(int(data)))) == 4:
        speed_data = "0000" + dec2hex(int(int(data)))
    elif len(dec2hex(int(data))) == 5:
        speed_data = "000" + dec2hex(int(data))
    elif len(dec2hex(int(data))) == 6:
        speed_data = "00" + dec2hex(int(data))
    elif len(dec2hex(int(data))) == 7:
        speed_data = "0" + dec2hex(int(data))
    elif len(dec2hex(int(data))) == 8:
        speed_data = dec2hex(int(data))
    result_data = speed_data#[4:8] + speed_data[0:4]
    return result_data


# 写数据给光源
def start_data_to_light(num):
    send_data = "52 58 44 0d 0a"
    send_data_to_light(send_data)

    time.sleep(1)
    send_data = "43 4f 4e" + hex_data(num)
    send_data_to_light(send_data)


def send_data_light():
    send_data = "D500101000000650040002F007A004E0078007800640032001C0042004400510094008F0000003F00480007290d0d0a" \
                "D500201000000AA006E006400CD008400DD00E100CA004900270073007600A300FF00F80000006900750014290d0d0a" \
                "A2000010000004F00000000006B0000003200B00099003E003900A200D8014C019700000000003500470000290d0d0a" \
                "CWF0101000000460053000000530000003D00F900E3005C0000000000000000004F00CA0000003A0000006F290d0d0a" \
                "TL84101000000350097004F00920045000C022B015500940032002100330032002B002E000B003000170070290d0d0a" \
                "D650101000000D00088006F00F9009200E400F8007C000B0000003D004E007A010901150000008900AC0000290d0d0a" \
                "D7501010000015500D600AB01BB00F0018901E100CE000000000047006600BE018A01FD000000D701210000290d0d0a" \
                "D500301000000E900A00078016300CA0193019801770070003800CD00CF013801BF01B50000009800AB0016290d0d0a"


    light_ser.write(send_data.encode('utf-8'))
    time.sleep(0.3)
    data = light_ser.readall()
    print(data)


def read_light_status():
    send_data = "53 58 43 0d 0a"
    data = send_data_to_light(send_data)
    print('recv data:',data,type(data))


# 测试校准
def test_measure(num):
    #hex_data_4
    send_data = "02 0e 00" + hex_data_4(num) +'00 0d 0d 0a'
    send_data_to_light(send_data)


import csv


if __name__ == '__main__':
    connect_light("COM8")
    time.sleep(1)
    temp = 1000
    for i in range(10000):
        print('发送数据:',temp)
        test_measure(temp)
        time.sleep(2)

        print(os.system('Measurement.exe'))

        data = []
        data.clear()
        with open("Measurement.csv", mode="r", encoding="utf-8-sig") as rf:
            reader = csv.reader(rf)
            for item in reader:
                print(item)
                data.append(item)

       #@ print('Ev:', str(data[4][1]))
        with open(r"./test.txt", 'a+') as f:
            #f.write(str(data[4][1]))
            #f.write(str(hex_data_41(temp)))
           # f.write(',')
            f.write(str(hex_data_41(int(float(data[4][1])))))

            f.write('\n')

        temp = temp - 20
    #red_light_status()
    # start_data_to_light(760)#570 665
    # time.sleep(1)
    # send_data_light()

    # send_data = "D500101000000650040002F007A004E0078007800640032001C0042004400510094008F0000003F00480007290d0d0aD500201000000AA006E006400CD008400DD00E100CA004900270073007600A300FF00F80000006900750014290d0d0aA2000010000004F00000000006B0000003200B00099003E003900A200D8014C019700000000003500470000290d0d0a" \
    #             "CWF0101000000460053000000530000003D00F900E3005C0000000000000000004F00CA0000003A0000006F290d0d0a" \
    #             "TL84101000000350097004F00920045000C022B015500940032002100330032002B002E000B003000170070290d0d0a" \
    #             "D650101000000D00088006F00F9009200E400F8007C000B0000003D004E007A010901150000008900AC0000290d0d0a" \
    #             "D7501010000015500D600AB01BB00F0018901E100CE000000000047006600BE018A01FD000000D701210000290d0d0a"
    #
    # print(len(send_data))
    # path = r'./色卡RGB.txt'
    # print(os.path.exists(path))
    # if os.path.exists(path) == True:
    #     pass
    #   #  print('path exist')  # 删除之后再创建
    #     os.remove(path)
    # else:
    #
    #     pass

  #  print(hex_data(100))
    #print(len('D500501000000AA006E006400CD008400DD00E100CA004900270073007600A300FF00F80000006900750014290d0d0a'))

    # send_data_light()
    # data = start_data_to_light()
    # print('data:',data)
   # send_data_light
   # set_qc_light(6)
    #switch_light('TL84','ON')# ON:打開 OFF:關閉
   # choose_light_num('CWF', '00000',100)# 色溫 燈管序號 照度技數