"""
光谱校正功能模块
实现通过调整电压值，将实测数据中780光谱及之前的波形逼近目标数据
使用二分法进行逼近
"""

import numpy as np
import csv
from typing import List, Tuple, Optional


class SpectrumCorrection:
    """光谱校正类"""
    
    def __init__(self, led_data_max_channel: np.ndarray, channel_wavelengths: List[int]):
        """
        初始化校正器

        Args:
            led_data_max_channel: LED数据最大通道数组，shape为(751, channels_num)
            channel_wavelengths: 每个通道对应的波长列表
        """
        self.led_data_max_channel = led_data_max_channel
        self.wavelengths = np.arange(350, 1101)  # 350-1100nm
        self.correction_range_start = 350  # 从350nm开始校正
        self.correction_range_end = 780  # 到780nm结束校正
        self.correction_indices = (self.wavelengths >= self.correction_range_start) & (self.wavelengths <= self.correction_range_end)

        # 通道对应的波长
        self.channel_wavelengths = channel_wavelengths

        # 不允许调节的通道（基于波长）
        self.forbidden_wavelengths = [810, 829, 828, 848, 880, 948]
        self.forbidden_channels = []
        for i, wl in enumerate(self.channel_wavelengths):
            if wl in self.forbidden_wavelengths:
                self.forbidden_channels.append(i)

        print(f"禁止调节的通道: {[i+1 for i in self.forbidden_channels]} (波长: {[self.channel_wavelengths[i] for i in self.forbidden_channels]})")
        
    def calculate_spectrum(self, voltage_values: List[float]) -> np.ndarray:
        """
        根据电压值计算光谱
        
        Args:
            voltage_values: 31个通道的电压值列表 (0-1000)
            
        Returns:
            计算得到的光谱数据 (751个点)
        """
        # 将电压值转换为0-1的比例
        voltage_ratios = np.array(voltage_values) / 1000.0
        
        # 计算光谱：每个通道的贡献 = LED数据 * 对应电压比例
        spectrum = np.sum(self.led_data_max_channel * voltage_ratios, axis=1)
        
        return spectrum
    
    def calculate_error(self, measured_spectrum: np.ndarray, target_spectrum: np.ndarray) -> float:
        """
        计算测量光谱与目标光谱在360-781nm范围内的误差

        Args:
            measured_spectrum: 测量光谱数据
            target_spectrum: 目标光谱数据

        Returns:
            均方根误差 (RMSE)
        """
        # 只计算360-781nm范围内的误差
        measured_part = measured_spectrum[self.correction_indices]
        target_part = target_spectrum[self.correction_indices]

        # 归一化处理
        if np.max(measured_part) > 0:
            measured_part = measured_part / np.max(measured_part)
        if np.max(target_part) > 0:
            target_part = target_part / np.max(target_part)

        # 计算均方根误差
        mse = np.mean((measured_part - target_part) ** 2)
        rmse = np.sqrt(mse)

        return rmse
    
    def binary_search_correction(self,
                                current_voltages: List[float],
                                measured_spectrum: np.ndarray,
                                target_spectrum: np.ndarray,
                                channel_to_adjust: int,
                                max_iterations: int = 10,
                                tolerance: float = 0.001,
                                max_change_percent: float = 0.1) -> Tuple[float, float]:
        """
        使用二分法对指定通道进行校正

        Args:
            current_voltages: 当前31个通道的电压值
            measured_spectrum: 当前测量的光谱数据
            target_spectrum: 目标光谱数据
            channel_to_adjust: 要调整的通道索引 (0-30)
            max_iterations: 最大迭代次数
            tolerance: 容差
            max_change_percent: 最大变化百分比 (0.1 = 10%)

        Returns:
            (最佳电压值, 最小误差)
        """
        # 保存原始电压值
        original_voltage = current_voltages[channel_to_adjust]
        best_voltage = original_voltage
        min_error = self.calculate_error(measured_spectrum, target_spectrum)

        # 限制搜索范围，确保变化幅度足够大
        max_change = max(100.0, original_voltage * max_change_percent)  # 至少变化100（对应输入框的10）
        low = max(0.0, original_voltage - max_change)
        high = min(1000.0, original_voltage + max_change)

        print(f"通道{channel_to_adjust+1}校正: 原值={original_voltage:.1f}, 搜索范围=[{low:.1f}, {high:.1f}]")
        
        for iteration in range(max_iterations):
            # 计算中点
            mid = (low + high) / 2.0
            
            # 测试中点电压值
            test_voltages = current_voltages.copy()
            test_voltages[channel_to_adjust] = mid
            
            # 计算对应的光谱
            test_spectrum = self.calculate_spectrum(test_voltages)
            
            # 计算误差
            error = self.calculate_error(test_spectrum, target_spectrum)
            
            # 如果找到更好的解
            if error < min_error:
                min_error = error
                best_voltage = mid
            
            # 判断搜索方向
            # 测试稍微增加电压的效果
            test_voltages_high = current_voltages.copy()
            test_voltages_high[channel_to_adjust] = mid + 1.0
            test_spectrum_high = self.calculate_spectrum(test_voltages_high)
            error_high = self.calculate_error(test_spectrum_high, target_spectrum)
            
            # 测试稍微减少电压的效果
            test_voltages_low = current_voltages.copy()
            test_voltages_low[channel_to_adjust] = mid - 1.0
            test_spectrum_low = self.calculate_spectrum(test_voltages_low)
            error_low = self.calculate_error(test_spectrum_low, target_spectrum)
            
            # 根据梯度方向调整搜索范围
            if error_low < error_high:
                high = mid
            else:
                low = mid
            
            # 检查收敛条件
            if abs(high - low) < tolerance:
                break

        # 确保变化幅度至少为10（对应输入框的1）
        if abs(best_voltage - original_voltage) < 10.0:
            # 如果变化太小，强制增加变化幅度
            if best_voltage > original_voltage:
                best_voltage = min(1000.0, original_voltage + 10.0)
            else:
                best_voltage = max(0.0, original_voltage - 10.0)
            print(f"变化幅度太小，强制调整到: {best_voltage:.1f}")

        print(f"通道{channel_to_adjust+1}校正结果: {original_voltage:.1f} -> {best_voltage:.1f}, 变化幅度: {abs(best_voltage - original_voltage):.1f}")
        return best_voltage, min_error
    
    def find_best_channel_to_adjust(self,
                                   current_voltages: List[float],
                                   measured_spectrum: np.ndarray,
                                   target_spectrum: np.ndarray) -> int:
        """
        找到对校正效果影响最大的通道，重点关注误差最大的波段

        Args:
            current_voltages: 当前电压值
            measured_spectrum: 测量光谱
            target_spectrum: 目标光谱

        Returns:
            最佳调整通道的索引
        """
        # 找到误差最大的波段
        measured_part = measured_spectrum[self.correction_indices]
        target_part = target_spectrum[self.correction_indices]

        # 归一化处理
        if np.max(measured_part) > 0:
            measured_part = measured_part / np.max(measured_part)
        if np.max(target_part) > 0:
            target_part = target_part / np.max(target_part)

        # 计算每个波长点的误差
        point_errors = np.abs(measured_part - target_part)
        max_error_index = np.argmax(point_errors)
        max_error_wavelength = self.wavelengths[self.correction_indices][max_error_index]

        print(f"最大误差波长: {max_error_wavelength}nm, 误差值: {point_errors[max_error_index]:.4f}")

        best_channel = 0
        max_sensitivity = 0.0

        # 测试每个通道对最大误差波段的影响
        for channel in range(len(current_voltages)):
            # 测试增加该通道电压的效果
            test_voltages = current_voltages.copy()
            test_change = max(100.0, current_voltages[channel] * 0.15)  # 至少变化100
            test_voltages[channel] = min(1000.0, test_voltages[channel] + test_change)

            test_spectrum = self.calculate_spectrum(test_voltages)

            # 计算该通道对最大误差波段的影响
            test_part = test_spectrum[self.correction_indices]
            if np.max(test_part) > 0:
                test_part = test_part / np.max(test_part)

            # 计算在最大误差波长处的改善程度
            original_error_at_max = abs(measured_part[max_error_index] - target_part[max_error_index])
            new_error_at_max = abs(test_part[max_error_index] - target_part[max_error_index])
            sensitivity = abs(original_error_at_max - new_error_at_max)

            print(f"通道{channel+1}对{max_error_wavelength}nm的影响: {sensitivity:.6f}")

            if sensitivity > max_sensitivity:
                max_sensitivity = sensitivity
                best_channel = channel

        print(f"选择调整通道{best_channel+1}，对最大误差波段的敏感度: {max_sensitivity:.6f}")

        return best_channel

    def find_nearest_channel(self, wavelength: int) -> int:
        """
        找到距离指定波长最近的通道

        Args:
            wavelength: 目标波长

        Returns:
            最近通道的索引，如果该通道被禁止则返回-1
        """
        min_distance = float('inf')
        nearest_channel = -1

        for i, channel_wl in enumerate(self.channel_wavelengths):
            if i in self.forbidden_channels:
                continue  # 跳过禁止调节的通道

            distance = abs(channel_wl - wavelength)
            if distance < min_distance:
                min_distance = distance
                nearest_channel = i

        return nearest_channel

    def adjust_channel_for_wavelength(self,
                                    current_voltages: List[float],
                                    measured_spectrum: np.ndarray,
                                    target_spectrum: np.ndarray,
                                    wavelength: int,
                                    max_iterations: int = 10) -> Tuple[List[float], bool]:
        """
        调整指定波长对应的通道，使该波长处的光谱值接近目标值

        Args:
            current_voltages: 当前电压值
            measured_spectrum: 测量光谱
            target_spectrum: 目标光谱
            wavelength: 要校正的波长
            max_iterations: 最大迭代次数

        Returns:
            (调整后的电压值, 是否成功改善)
        """
        # 找到最近的通道
        channel = self.find_nearest_channel(wavelength)
        if channel == -1:
            print(f"波长{wavelength}nm没有可调节的通道")
            return current_voltages, False

        print(f"调整波长{wavelength}nm，使用通道{channel+1} (波长{self.channel_wavelengths[channel]}nm)")

        # 获取该波长在光谱数组中的索引
        wavelength_index = wavelength - 350
        if wavelength_index < 0 or wavelength_index >= len(measured_spectrum):
            return current_voltages, False

        # 归一化处理
        measured_normalized = measured_spectrum / np.max(measured_spectrum) if np.max(measured_spectrum) > 0 else measured_spectrum
        target_normalized = target_spectrum / np.max(target_spectrum) if np.max(target_spectrum) > 0 else target_spectrum

        current_value = measured_normalized[wavelength_index]
        target_value = target_normalized[wavelength_index]

        print(f"波长{wavelength}nm: 当前值={current_value:.4f}, 目标值={target_value:.4f}")

        if abs(current_value - target_value) < 0.01:  # 已经足够接近
            print(f"波长{wavelength}nm已经足够接近目标值")
            return current_voltages, False

        # 调整电压
        adjusted_voltages = current_voltages.copy()
        original_voltage = current_voltages[channel]

        # 根据差异方向调整电压
        if current_value < target_value:
            # 需要增加该波长的强度
            voltage_change = min(100.0, original_voltage * 0.2)  # 增加20%或100，取较小值
            adjusted_voltages[channel] = min(1000.0, original_voltage + voltage_change)
        else:
            # 需要减少该波长的强度
            voltage_change = min(100.0, original_voltage * 0.2)
            adjusted_voltages[channel] = max(0.0, original_voltage - voltage_change)

        print(f"通道{channel+1}电压: {original_voltage:.1f} -> {adjusted_voltages[channel]:.1f}")

        # 验证调整效果
        new_spectrum = self.calculate_spectrum(adjusted_voltages)
        new_normalized = new_spectrum / np.max(new_spectrum) if np.max(new_spectrum) > 0 else new_spectrum
        new_value = new_normalized[wavelength_index]

        improvement = abs(current_value - target_value) - abs(new_value - target_value)
        print(f"波长{wavelength}nm调整后: {new_value:.4f}, 改善程度: {improvement:.4f}")

        return adjusted_voltages, improvement > 0.001

    def correct_spectrum_pointwise(self,
                                 current_voltages: List[float],
                                 measured_spectrum: np.ndarray,
                                 target_spectrum: np.ndarray,
                                 max_iterations: int = 50) -> Tuple[List[float], float]:
        """
        逐点校正光谱，每次找到差异最大的点并调整对应通道

        Args:
            current_voltages: 当前电压值
            measured_spectrum: 测量光谱
            target_spectrum: 目标光谱
            max_iterations: 最大迭代次数

        Returns:
            (校正后的电压值, 最终误差)
        """
        voltages = current_voltages.copy()

        for iteration in range(max_iterations):
            # 计算当前光谱
            current_spectrum = self.calculate_spectrum(voltages)

            # 只考虑350-780nm范围
            measured_part = current_spectrum[self.correction_indices]
            target_part = target_spectrum[self.correction_indices]

            # 归一化
            if np.max(measured_part) > 0:
                measured_part = measured_part / np.max(measured_part)
            if np.max(target_part) > 0:
                target_part = target_part / np.max(target_part)

            # 找到差异最大的波长点
            differences = np.abs(measured_part - target_part)
            max_diff_index = np.argmax(differences)
            max_diff_wavelength = self.wavelengths[self.correction_indices][max_diff_index]
            max_diff_value = differences[max_diff_index]

            print(f"\n迭代{iteration+1}: 最大差异在{max_diff_wavelength}nm, 差异值: {max_diff_value:.4f}")

            if max_diff_value < 0.02:  # 差异足够小
                print("所有点的差异都足够小，校正完成")
                break

            # 调整该波长对应的通道
            voltages, improved = self.adjust_channel_for_wavelength(
                voltages, current_spectrum, target_spectrum, int(max_diff_wavelength)
            )

            if not improved:
                print(f"波长{max_diff_wavelength}nm无法进一步改善")
                # 尝试调整次大差异的点
                differences[max_diff_index] = 0  # 排除当前点
                if np.max(differences) > 0.02:
                    second_max_index = np.argmax(differences)
                    second_max_wavelength = self.wavelengths[self.correction_indices][second_max_index]
                    print(f"尝试调整次大差异点: {second_max_wavelength}nm")
                    voltages, _ = self.adjust_channel_for_wavelength(
                        voltages, current_spectrum, target_spectrum, int(second_max_wavelength)
                    )

        # 计算最终误差
        final_spectrum = self.calculate_spectrum(voltages)
        final_error = self.calculate_error(final_spectrum, target_spectrum)

        print(f"\n校正完成，共进行{iteration+1}次迭代，最终误差: {final_error:.6f}")

        return voltages, final_error

    def correct_spectrum(self,
                        current_voltages: List[float],
                        measured_spectrum: np.ndarray,
                        target_spectrum: np.ndarray) -> Tuple[List[float], float]:
        """
        执行一次光谱校正
        
        Args:
            current_voltages: 当前31个通道的电压值
            measured_spectrum: 当前测量的光谱数据 (751个点)
            target_spectrum: 目标光谱数据 (751个点)
            
        Returns:
            (校正后的电压值列表, 校正后的误差)
        """
        # 找到最佳调整通道
        best_channel = self.find_best_channel_to_adjust(
            current_voltages, measured_spectrum, target_spectrum
        )
        
        # 对该通道进行二分法校正
        best_voltage, min_error = self.binary_search_correction(
            current_voltages, measured_spectrum, target_spectrum, best_channel
        )
        
        # 更新电压值
        corrected_voltages = current_voltages.copy()
        corrected_voltages[best_channel] = best_voltage
        
        return corrected_voltages, min_error


def save_correction_log(voltages: List[float], error: float, iteration: int, filename: str = "correction_log.csv"):
    """
    保存校正日志
    
    Args:
        voltages: 电压值列表
        error: 当前误差
        iteration: 迭代次数
        filename: 日志文件名
    """
    import os
    
    # 检查文件是否存在，如果不存在则创建表头
    file_exists = os.path.exists(filename)
    
    with open(filename, 'a', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        
        if not file_exists:
            # 写入表头
            header = ['Iteration', 'Error'] + [f'Channel_{i+1}' for i in range(len(voltages))]
            writer.writerow(header)
        
        # 写入数据
        row = [iteration, error] + voltages
        writer.writerow(row)
