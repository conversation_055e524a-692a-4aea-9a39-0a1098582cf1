"""
光谱校正功能模块
实现通过调整电压值，将实测数据中780光谱及之前的波形逼近目标数据
使用二分法进行逼近
"""

import numpy as np
import csv
from typing import List, Tuple, Optional


class SpectrumCorrection:
    """光谱校正类"""
    
    def __init__(self, led_data_max_channel: np.ndarray):
        """
        初始化校正器
        
        Args:
            led_data_max_channel: LED数据最大通道数组，shape为(751, channels_num)
        """
        self.led_data_max_channel = led_data_max_channel
        self.wavelengths = np.arange(350, 1101)  # 350-1100nm
        self.correction_range_end = 780  # 只校正780nm及之前的数据
        self.correction_indices = self.wavelengths <= self.correction_range_end
        
    def calculate_spectrum(self, voltage_values: List[float]) -> np.ndarray:
        """
        根据电压值计算光谱
        
        Args:
            voltage_values: 31个通道的电压值列表 (0-1000)
            
        Returns:
            计算得到的光谱数据 (751个点)
        """
        # 将电压值转换为0-1的比例
        voltage_ratios = np.array(voltage_values) / 1000.0
        
        # 计算光谱：每个通道的贡献 = LED数据 * 对应电压比例
        spectrum = np.sum(self.led_data_max_channel * voltage_ratios, axis=1)
        
        return spectrum
    
    def calculate_error(self, measured_spectrum: np.ndarray, target_spectrum: np.ndarray) -> float:
        """
        计算测量光谱与目标光谱在780nm及之前的误差
        
        Args:
            measured_spectrum: 测量光谱数据
            target_spectrum: 目标光谱数据
            
        Returns:
            均方根误差 (RMSE)
        """
        # 只计算780nm及之前的误差
        measured_part = measured_spectrum[self.correction_indices]
        target_part = target_spectrum[self.correction_indices]
        
        # 归一化处理
        if np.max(measured_part) > 0:
            measured_part = measured_part / np.max(measured_part)
        if np.max(target_part) > 0:
            target_part = target_part / np.max(target_part)
        
        # 计算均方根误差
        mse = np.mean((measured_part - target_part) ** 2)
        rmse = np.sqrt(mse)
        
        return rmse
    
    def binary_search_correction(self, 
                                current_voltages: List[float],
                                measured_spectrum: np.ndarray,
                                target_spectrum: np.ndarray,
                                channel_to_adjust: int,
                                max_iterations: int = 20,
                                tolerance: float = 0.001) -> Tuple[float, float]:
        """
        使用二分法对指定通道进行校正
        
        Args:
            current_voltages: 当前31个通道的电压值
            measured_spectrum: 当前测量的光谱数据
            target_spectrum: 目标光谱数据
            channel_to_adjust: 要调整的通道索引 (0-30)
            max_iterations: 最大迭代次数
            tolerance: 容差
            
        Returns:
            (最佳电压值, 最小误差)
        """
        # 保存原始电压值
        original_voltage = current_voltages[channel_to_adjust]
        best_voltage = original_voltage
        min_error = self.calculate_error(measured_spectrum, target_spectrum)
        
        # 二分法搜索范围
        low = 0.0
        high = 1000.0
        
        for iteration in range(max_iterations):
            # 计算中点
            mid = (low + high) / 2.0
            
            # 测试中点电压值
            test_voltages = current_voltages.copy()
            test_voltages[channel_to_adjust] = mid
            
            # 计算对应的光谱
            test_spectrum = self.calculate_spectrum(test_voltages)
            
            # 计算误差
            error = self.calculate_error(test_spectrum, target_spectrum)
            
            # 如果找到更好的解
            if error < min_error:
                min_error = error
                best_voltage = mid
            
            # 判断搜索方向
            # 测试稍微增加电压的效果
            test_voltages_high = current_voltages.copy()
            test_voltages_high[channel_to_adjust] = mid + 1.0
            test_spectrum_high = self.calculate_spectrum(test_voltages_high)
            error_high = self.calculate_error(test_spectrum_high, target_spectrum)
            
            # 测试稍微减少电压的效果
            test_voltages_low = current_voltages.copy()
            test_voltages_low[channel_to_adjust] = mid - 1.0
            test_spectrum_low = self.calculate_spectrum(test_voltages_low)
            error_low = self.calculate_error(test_spectrum_low, target_spectrum)
            
            # 根据梯度方向调整搜索范围
            if error_low < error_high:
                high = mid
            else:
                low = mid
            
            # 检查收敛条件
            if abs(high - low) < tolerance:
                break
        
        return best_voltage, min_error
    
    def find_best_channel_to_adjust(self,
                                   current_voltages: List[float],
                                   measured_spectrum: np.ndarray,
                                   target_spectrum: np.ndarray) -> int:
        """
        找到对校正效果影响最大的通道
        
        Args:
            current_voltages: 当前电压值
            measured_spectrum: 测量光谱
            target_spectrum: 目标光谱
            
        Returns:
            最佳调整通道的索引
        """
        best_channel = 0
        max_sensitivity = 0.0
        
        current_error = self.calculate_error(measured_spectrum, target_spectrum)
        
        # 测试每个通道的敏感度
        for channel in range(len(current_voltages)):
            # 测试增加该通道电压的效果
            test_voltages = current_voltages.copy()
            test_voltages[channel] = min(1000.0, test_voltages[channel] + 10.0)
            
            test_spectrum = self.calculate_spectrum(test_voltages)
            test_error = self.calculate_error(test_spectrum, target_spectrum)
            
            # 计算敏感度（误差变化量）
            sensitivity = abs(current_error - test_error)
            
            if sensitivity > max_sensitivity:
                max_sensitivity = sensitivity
                best_channel = channel
        
        return best_channel
    
    def correct_spectrum(self,
                        current_voltages: List[float],
                        measured_spectrum: np.ndarray,
                        target_spectrum: np.ndarray) -> Tuple[List[float], float]:
        """
        执行一次光谱校正
        
        Args:
            current_voltages: 当前31个通道的电压值
            measured_spectrum: 当前测量的光谱数据 (751个点)
            target_spectrum: 目标光谱数据 (751个点)
            
        Returns:
            (校正后的电压值列表, 校正后的误差)
        """
        # 找到最佳调整通道
        best_channel = self.find_best_channel_to_adjust(
            current_voltages, measured_spectrum, target_spectrum
        )
        
        # 对该通道进行二分法校正
        best_voltage, min_error = self.binary_search_correction(
            current_voltages, measured_spectrum, target_spectrum, best_channel
        )
        
        # 更新电压值
        corrected_voltages = current_voltages.copy()
        corrected_voltages[best_channel] = best_voltage
        
        return corrected_voltages, min_error


def save_correction_log(voltages: List[float], error: float, iteration: int, filename: str = "correction_log.csv"):
    """
    保存校正日志
    
    Args:
        voltages: 电压值列表
        error: 当前误差
        iteration: 迭代次数
        filename: 日志文件名
    """
    import os
    
    # 检查文件是否存在，如果不存在则创建表头
    file_exists = os.path.exists(filename)
    
    with open(filename, 'a', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        
        if not file_exists:
            # 写入表头
            header = ['Iteration', 'Error'] + [f'Channel_{i+1}' for i in range(len(voltages))]
            writer.writerow(header)
        
        # 写入数据
        row = [iteration, error] + voltages
        writer.writerow(row)
