<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1694</width>
    <height>1099</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>正印多光谱控制软件v3.1 20250624</string>
  </property>
  <property name="windowIcon">
   <iconset>
    <normaloff>C:/Users/<USER>/.designer/backup/icon.ico</normaloff>C:/Users/<USER>/.designer/backup/icon.ico</iconset>
  </property>
  <widget class="QWidget" name="centralwidget">
   <property name="styleSheet">
    <string notr="true">*{
	font-family: &quot;Microsoft YaHei&quot;;
}
/*背景颜色*/
#centralwidget{
background-color:#f4f6fa;
border-radius:10px;
}
#Top_widget{
background-color:#ffffff;
border-radius:10px;
}
/*Label*/
QLabel{
	font-size:1em;
	font-family: &quot;Microsoft YaHei&quot;;
}
/*输入框*/
QLineEdit{
	background-color: #fff;
    background-image: none;
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    color: #606266;
    font-size: 12pt;
    height: 28px;
    line-height: 28px;
    outline: none;
    padding: 0 15px;
    width: 100%;
}
QLineEdit:focus {    
	outline: none;
    border-color: #409eff;
}
/*下拉栏*/
QComboBox,QLineEdit,QSpinBox,QDoubleSpinBox{background-color: #fff;
    background-image: none;
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    color: #606266;
   font-size: 12pt;
    height: 28px;
    line-height: 28px;
    outline: none;
    padding: 0 15px;
    width: 100%;}
QComboBox:hover,QLineEdit:hover,QSpinBox:hover,QDoubleSpinBox:hover{border: 3px solid #55aaff;}
QComboBox::drop-down {subcontrol-origin: padding;
subcontrol-position: top right;width: 25px; border-radius: 3px;
background-image: url(:/icons/Source/icons/cil-arrow-bottom.png);
background-position: center;background-repeat: no-reperat;}
QComboBox QAbstractItemView {selection-background-color: #55aaff;
}
/*勾选框*/
QCheckBox::indicator ,QRadioButton::indicator{border: 3px solid #dfdfdf;width: 15px;height: 15px;border-radius: 5px;background: #ffffff;}
QCheckBox::indicator:hover,QRadioButton::indicator:hover {background-color:#dfdfdf;border: 3px solid #ffffff;}
QCheckBox::indicator:checked ,QRadioButton::indicator:checked{background: 3px solid #55aaff;}
QCheckBox ,QRadioButton{color: #808080;
}
/*Spin控件*/
QDateTimeEdit::up-button,QTimeEdit::up-button,QDoubleSpinBox::up-button,QSpinBox::up-button {subcontrol-origin:border;subcontrol-position:right;
image: url(:/icons/Source/icons/cil-plus.png);width: 20px;height: 20px; 
}
QDateTimeEdit::down-button,QTimeEdit::down-button,QDoubleSpinBox::down-button,QSpinBox::down-button {subcontrol-origin:border;subcontrol-position:left;
image: url(:/icons/Source/icons/cil-minus.png);width: 20px;height: 60px;
}
QDateTimeEdit::up-button:pressed,QTimeEdit::up-button:pressed,QDoubleSpinBox::up-button:pressed,QSpinBox::up-button:pressed{subcontrol-origin:border;
subcontrol-position:right;width: 20px;height: 20px; 
}
QDateTimeEdit::down-button:pressed,QTimeEdit::down-button:pressed,QDoubleSpinBox::down-button:pressed,QSpinBox::down-button:pressed{subcontrol-position:left;width: 20px;height: 20px;
}
/*滑动条*/
QSlider::sub-page:horizontal {background: #dfdfdf;border-radius: 2px;margin-top:8px;margin-bottom:8px;}
QSlider::add-page:horizontal {background: #ffffff;border: 0px solid #ffffff;border-radius: 2px;margin-top:9px;margin-bottom:9px;}
QSlider::handle:horizontal {background: #55aaff;width: 2px;border: 1px solid #3c78b4;
border-radius: 2px; margin-top:6px;margin-bottom:6px;}
QSlider::handle:horizontal:hover {background: #55aaff;width: 10px;border: 1px solid #dfdfdf;
border-radius: 5px; margin-top:4px;margin-bottom:4px;}
/*进度条*/
QProgressBar{color:#808080;background: #FFFFFF;border-radius: 10px;border:2px solid #dfdfdf;font-size:12pt;}QProgressBar::chunk{background: qlineargradient(spread:pad,x1:0,y1:0,x2:1,y2:0,stop:0 #01FAFF,stop:1 #26B4FF); border-radius: 8px;}
/*组盒*/
QGroupBox::title {
	color: rgb(100,100,100);
	subcontrol-origin: margin;
    subcontrol-position: top center; /* position at the top center */
	font-size: 15px;
	background-color:#dfdfdf;
	padding:2px 6px 2px 6px;

}
QGroupBox {background-color: #ffffff;border: 2px solid #dfdfdf;border-radius:5px}
/*按钮*/
QPushButton {
    white-space: nowrap;
    text-align: center;
    outline: none;
    margin: 0;
	font-size: 12pt;
 	background-color: #409eff;border: none;color: #ffffff;border-radius:5px;padding:5px 10px;
}
QPushButton:hover {background-color:  #66b1ff;border: none; }
QPushButton:pressed {background-color: #3a8ee6;border: 3px solid #dfdfdf; }
QPushButton::disabled{color: #ffffff;background-color: #a0cfff;
}

/*表格*/
QTableWidget &gt; QPushButton{margin:2px}
QTableWidget QHeaderView{border-bottom:1px solid #3c78b4;}
QTableWidget QHeaderView::section:horizontal{padding: 3px;border: 1px solid #ebeef5;
border-left:0px;
background-color: white;color:#909399;min-height:30px;}
QTableWidget{border-radius: 5px;alternate-background-color:#dfdfdf;selection-background-color:#dfdfdf; 
	background-color: qlineargradient(spread:pad, x1:0.505682, y1:0, x2:0.483, y2:1, stop:0 rgba(255, 255, 255, 255), stop:1 rgba(255, 255, 255, 198));
gridline-color:#ebeef5;color:#55aaff;padding: 10px;border-radius: 5px;outline: none;}
QTableWidget::item{border-color: #ffffff;padding-left: 5px;padding-right: 5px;gridline-color: #ebeef5;}
QTableWidget::item:selected{background-color: #f5f7fa;color: #606295;
}
/*拉条*/
QScrollBar:horizontal {background: #ffffff;height: 8px;margin: 0px 21px 0 21px;}
QScrollBar::handle:horizontal {background: #55aaff;min-width: 25px;border-radius: 4px}
QScrollBar::add-line:horizontal {background: #dfdfdf;width: 20px;border-top-right-radius: 4px;
border-bottom-right-radius: 4px;subcontrol-position: right;subcontrol-origin: margin;}
QScrollBar::sub-line:horizontal {background: #dfdfdf;width: 20px;
border-top-left-radius: 4px;border-bottom-left-radius: 4px;subcontrol-position: left;subcontrol-origin: margin;}
QScrollBar:vertical {background-color: #ffffff;width: 8px;margin: 21px 0 21px 0;}
QScrollBar::handle:vertical { background: #55aaff;min-height: 25px;border-radius: 4px}
QScrollBar::add-line:vertical {background: #dfdfdf;height: 20px;border-bottom-left-radius: 4px;border-bottom-right-radius: 4px;
subcontrol-position: bottom;subcontrol-origin: margin;}
QScrollBar::sub-line:vertical {border: none;background: #dfdfdf;height: 20px;
border-top-left-radius: 4px;border-top-right-radius: 4px;subcontrol-position: top;subcontrol-origin: margin;
}
QTabBar::pane{
	border:none;
	
}

QTabBar::tab{
    border-top-left-radius: 0px;
    border-top-right-radius: 0px;
    min-height:40px;
	min-width:100px;
    font:10pt;
    padding: 0px;
	border-bottom:2px solid  rgb(228, 231, 237);
	margin:0 10px;
}
QTabBar::tab::selected{
	color:rgb(64, 158, 255);
	border-bottom:2px solid  rgb(64, 158, 255);
}
/*下拉菜单*/
QToolBox::tab {color: #808080;background-color: white;padding-left: 0px;padding-right: 0px;border-bottom: 1px solid #dfdfdf;image-position: Right center;image: url(:/icons/Source/icons/cil-arrow-top.png);font-size:15px;icon-size:48px;}
QToolBox::tab:hover { color:#409eff}
QToolBox::tab:selected { font-size:18px;color:black;image: url(:/icons/Source/icons/cil-arrow-bottom.png);
}
#toolBox{icon-size: 40px 10px;background-color: white;}
QToolBox QPushButton{background-color:white;color:#303133;height:20px;}
QToolBox QPushButton:hover{background-color:#ececec;color:#303133;}

QToolBox QLabel{width:100px;height:100px;}

/*颜色说明
控件颜色:背景色(白)#ffffff; 选中色(深蓝)#3c78b4;上方色浅(蓝)#55aaff; 边框色(灰)#dfdfdf,字体(深灰)#808080;
背景颜色rgba(170, 235, 239, 255);rgba(189, 210, 243, 255);rgba(97, 108,125,255);rgba(117,161, 164, 255);*/</string>
   </property>
   <layout class="QGridLayout" name="gridLayout_3">
    <item row="1" column="1">
     <widget class="QWidget" name="widget_39" native="true">
      <property name="styleSheet">
       <string notr="true"/>
      </property>
      <layout class="QHBoxLayout" name="horizontalLayout_4">
       <property name="spacing">
        <number>0</number>
       </property>
       <property name="leftMargin">
        <number>0</number>
       </property>
       <property name="topMargin">
        <number>0</number>
       </property>
       <property name="rightMargin">
        <number>0</number>
       </property>
       <property name="bottomMargin">
        <number>0</number>
       </property>
      </layout>
     </widget>
    </item>
    <item row="0" column="0">
     <widget class="QWidget" name="Top_widget" native="true">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>285</width>
        <height>60</height>
       </size>
      </property>
      <property name="maximumSize">
       <size>
        <width>16777215</width>
        <height>60</height>
       </size>
      </property>
      <property name="font">
       <font>
        <family>Microsoft YaHei</family>
       </font>
      </property>
      <property name="layoutDirection">
       <enum>Qt::LeftToRight</enum>
      </property>
      <property name="autoFillBackground">
       <bool>false</bool>
      </property>
      <layout class="QGridLayout" name="gridLayout_7">
       <property name="leftMargin">
        <number>0</number>
       </property>
       <property name="topMargin">
        <number>0</number>
       </property>
       <property name="rightMargin">
        <number>0</number>
       </property>
       <property name="bottomMargin">
        <number>0</number>
       </property>
       <item row="0" column="0">
        <widget class="QWidget" name="widget_38" native="true">
         <property name="minimumSize">
          <size>
           <width>290</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>16777215</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="font">
          <font>
           <family>Microsoft YaHei</family>
          </font>
         </property>
         <layout class="QHBoxLayout" name="horizontalLayout_2">
          <property name="leftMargin">
           <number>20</number>
          </property>
          <property name="topMargin">
           <number>0</number>
          </property>
          <property name="rightMargin">
           <number>0</number>
          </property>
          <property name="bottomMargin">
           <number>0</number>
          </property>
          <item>
           <widget class="QLabel" name="colorspace_logo">
            <property name="minimumSize">
             <size>
              <width>180</width>
              <height>60</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>180</width>
              <height>60</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true"/>
            </property>
            <property name="frameShape">
             <enum>QFrame::NoFrame</enum>
            </property>
            <property name="text">
             <string/>
            </property>
            <property name="pixmap">
             <pixmap resource="CMS.qrc">:/logo/Source/logo.png</pixmap>
            </property>
            <property name="scaledContents">
             <bool>true</bool>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_4">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </widget>
       </item>
      </layout>
     </widget>
    </item>
    <item row="1" column="0">
     <widget class="QTabWidget" name="tabWidget_5">
      <property name="currentIndex">
       <number>0</number>
      </property>
      <widget class="QWidget" name="tab_38">
       <attribute name="title">
        <string>多光谱光源</string>
       </attribute>
       <layout class="QGridLayout" name="gridLayout_166">
        <item row="0" column="0">
         <layout class="QVBoxLayout" name="verticalLayout_2" stretch="0">
          <property name="spacing">
           <number>0</number>
          </property>
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout" stretch="1,2,1">
            <item>
             <widget class="QWidget" name="widget_40" native="true">
              <property name="styleSheet">
               <string notr="true"/>
              </property>
              <layout class="QHBoxLayout" name="horizontalLayout_5">
               <property name="spacing">
                <number>0</number>
               </property>
               <property name="leftMargin">
                <number>0</number>
               </property>
               <property name="topMargin">
                <number>0</number>
               </property>
               <property name="rightMargin">
                <number>0</number>
               </property>
               <property name="bottomMargin">
                <number>0</number>
               </property>
               <item>
                <widget class="QTabWidget" name="tabWidget">
                 <property name="minimumSize">
                  <size>
                   <width>449</width>
                   <height>200</height>
                  </size>
                 </property>
                 <property name="maximumSize">
                  <size>
                   <width>9999</width>
                   <height>16777215</height>
                  </size>
                 </property>
                 <property name="currentIndex">
                  <number>0</number>
                 </property>
                 <widget class="QWidget" name="tab">
                  <attribute name="title">
                   <string> 数据 </string>
                  </attribute>
                  <layout class="QHBoxLayout" name="horizontalLayout_12">
                   <item>
                    <widget class="QScrollArea" name="scrollArea_2">
                     <property name="maximumSize">
                      <size>
                       <width>16777215</width>
                       <height>999999</height>
                      </size>
                     </property>
                     <property name="verticalScrollBarPolicy">
                      <enum>Qt::ScrollBarAsNeeded</enum>
                     </property>
                     <property name="horizontalScrollBarPolicy">
                      <enum>Qt::ScrollBarAlwaysOff</enum>
                     </property>
                     <property name="widgetResizable">
                      <bool>true</bool>
                     </property>
                     <widget class="QWidget" name="scrollAreaWidgetContents_2">
                      <property name="geometry">
                       <rect>
                        <x>0</x>
                        <y>0</y>
                        <width>413</width>
                        <height>595</height>
                       </rect>
                      </property>
                      <layout class="QVBoxLayout" name="verticalLayout_45">
                       <item>
                        <layout class="QVBoxLayout" name="verticalLayout_41">
                         <item>
                          <widget class="QGroupBox" name="groupBox">
                           <property name="minimumSize">
                            <size>
                             <width>0</width>
                             <height>250</height>
                            </size>
                           </property>
                           <property name="font">
                            <font>
                             <family>Microsoft YaHei</family>
                             <pointsize>8</pointsize>
                            </font>
                           </property>
                           <property name="title">
                            <string>连接</string>
                           </property>
                           <layout class="QGridLayout" name="gridLayout_5">
                            <item row="0" column="0">
                             <layout class="QHBoxLayout" name="horizontalLayout_21">
                              <item>
                               <widget class="QLabel" name="label">
                                <property name="font">
                                 <font>
                                  <family>Microsoft YaHei</family>
                                  <pointsize>12</pointsize>
                                 </font>
                                </property>
                                <property name="text">
                                 <string>光源：</string>
                                </property>
                               </widget>
                              </item>
                              <item>
                               <widget class="QComboBox" name="comboBox_serials">
                                <property name="minimumSize">
                                 <size>
                                  <width>140</width>
                                  <height>0</height>
                                 </size>
                                </property>
                                <property name="maximumSize">
                                 <size>
                                  <width>80</width>
                                  <height>16777215</height>
                                 </size>
                                </property>
                               </widget>
                              </item>
                              <item>
                               <widget class="QComboBox" name="comboBox_serials_2">
                                <property name="minimumSize">
                                 <size>
                                  <width>140</width>
                                  <height>0</height>
                                 </size>
                                </property>
                                <property name="maximumSize">
                                 <size>
                                  <width>80</width>
                                  <height>16777215</height>
                                 </size>
                                </property>
                               </widget>
                              </item>
                             </layout>
                            </item>
                            <item row="1" column="0">
                             <layout class="QHBoxLayout" name="horizontalLayout_23">
                              <item>
                               <widget class="QLabel" name="label_10">
                                <property name="font">
                                 <font>
                                  <family>Microsoft YaHei</family>
                                  <pointsize>12</pointsize>
                                 </font>
                                </property>
                                <property name="text">
                                 <string>光源：</string>
                                </property>
                               </widget>
                              </item>
                              <item>
                               <widget class="QComboBox" name="comboBox_serials_3">
                                <property name="minimumSize">
                                 <size>
                                  <width>140</width>
                                  <height>0</height>
                                 </size>
                                </property>
                                <property name="maximumSize">
                                 <size>
                                  <width>80</width>
                                  <height>16777215</height>
                                 </size>
                                </property>
                               </widget>
                              </item>
                              <item>
                               <widget class="QComboBox" name="comboBox_serials_4">
                                <property name="minimumSize">
                                 <size>
                                  <width>140</width>
                                  <height>0</height>
                                 </size>
                                </property>
                                <property name="maximumSize">
                                 <size>
                                  <width>80</width>
                                  <height>16777215</height>
                                 </size>
                                </property>
                               </widget>
                              </item>
                             </layout>
                            </item>
                            <item row="2" column="0">
                             <layout class="QHBoxLayout" name="horizontalLayout_24">
                              <item>
                               <widget class="QLabel" name="label_11">
                                <property name="font">
                                 <font>
                                  <family>Microsoft YaHei</family>
                                  <pointsize>12</pointsize>
                                 </font>
                                </property>
                                <property name="text">
                                 <string>光源：</string>
                                </property>
                               </widget>
                              </item>
                              <item>
                               <widget class="QComboBox" name="comboBox_serials_5">
                                <property name="minimumSize">
                                 <size>
                                  <width>140</width>
                                  <height>0</height>
                                 </size>
                                </property>
                                <property name="maximumSize">
                                 <size>
                                  <width>80</width>
                                  <height>16777215</height>
                                 </size>
                                </property>
                               </widget>
                              </item>
                              <item>
                               <widget class="QComboBox" name="comboBox_serials_6">
                                <property name="minimumSize">
                                 <size>
                                  <width>140</width>
                                  <height>0</height>
                                 </size>
                                </property>
                                <property name="maximumSize">
                                 <size>
                                  <width>80</width>
                                  <height>16777215</height>
                                 </size>
                                </property>
                               </widget>
                              </item>
                             </layout>
                            </item>
                            <item row="3" column="0">
                             <layout class="QHBoxLayout" name="horizontalLayout_25">
                              <item>
                               <spacer name="horizontalSpacer">
                                <property name="orientation">
                                 <enum>Qt::Horizontal</enum>
                                </property>
                                <property name="sizeHint" stdset="0">
                                 <size>
                                  <width>40</width>
                                  <height>20</height>
                                 </size>
                                </property>
                               </spacer>
                              </item>
                              <item>
                               <widget class="QPushButton" name="pushButton_serials_connect">
                                <property name="minimumSize">
                                 <size>
                                  <width>100</width>
                                  <height>0</height>
                                 </size>
                                </property>
                                <property name="maximumSize">
                                 <size>
                                  <width>100</width>
                                  <height>30</height>
                                 </size>
                                </property>
                                <property name="text">
                                 <string>连接</string>
                                </property>
                               </widget>
                              </item>
                              <item>
                               <widget class="QPushButton" name="pushButton_light_status">
                                <property name="maximumSize">
                                 <size>
                                  <width>20</width>
                                  <height>30</height>
                                 </size>
                                </property>
                                <property name="text">
                                 <string/>
                                </property>
                               </widget>
                              </item>
                             </layout>
                            </item>
                            <item row="4" column="0">
                             <layout class="QHBoxLayout" name="horizontalLayout_19">
                              <item>
                               <widget class="QLabel" name="label_4">
                                <property name="font">
                                 <font>
                                  <family>Microsoft YaHei</family>
                                  <pointsize>12</pointsize>
                                 </font>
                                </property>
                                <property name="text">
                                 <string>仪器：</string>
                                </property>
                               </widget>
                              </item>
                              <item>
                               <widget class="QComboBox" name="measure_comboBox">
                                <property name="minimumSize">
                                 <size>
                                  <width>140</width>
                                  <height>0</height>
                                 </size>
                                </property>
                                <property name="maximumSize">
                                 <size>
                                  <width>140</width>
                                  <height>16777215</height>
                                 </size>
                                </property>
                               </widget>
                              </item>
                              <item>
                               <widget class="QLabel" name="label_2">
                                <property name="font">
                                 <font>
                                  <family>Microsoft YaHei</family>
                                  <pointsize>12</pointsize>
                                 </font>
                                </property>
                                <property name="text">
                                 <string>协议：</string>
                                </property>
                               </widget>
                              </item>
                              <item>
                               <widget class="QComboBox" name="comboBox_protocol">
                                <property name="minimumSize">
                                 <size>
                                  <width>80</width>
                                  <height>0</height>
                                 </size>
                                </property>
                                <property name="maximumSize">
                                 <size>
                                  <width>80</width>
                                  <height>16777215</height>
                                 </size>
                                </property>
                               </widget>
                              </item>
                             </layout>
                            </item>
                            <item row="5" column="0">
                             <layout class="QHBoxLayout" name="horizontalLayout_20">
                              <item>
                               <widget class="QLabel" name="label_8">
                                <property name="font">
                                 <font>
                                  <family>Microsoft YaHei</family>
                                  <pointsize>12</pointsize>
                                 </font>
                                </property>
                                <property name="text">
                                 <string>端口:</string>
                                </property>
                               </widget>
                              </item>
                              <item>
                               <widget class="QComboBox" name="comboBox_cs_lm01">
                                <property name="minimumSize">
                                 <size>
                                  <width>140</width>
                                  <height>0</height>
                                 </size>
                                </property>
                                <property name="maximumSize">
                                 <size>
                                  <width>140</width>
                                  <height>16777215</height>
                                 </size>
                                </property>
                               </widget>
                              </item>
                              <item>
                               <widget class="QPushButton" name="pushButton_cs_lm01">
                                <property name="text">
                                 <string>连接</string>
                                </property>
                               </widget>
                              </item>
                              <item>
                               <widget class="QPushButton" name="pushButton_cs_lm01_status">
                                <property name="maximumSize">
                                 <size>
                                  <width>20</width>
                                  <height>16777215</height>
                                 </size>
                                </property>
                                <property name="text">
                                 <string/>
                                </property>
                               </widget>
                              </item>
                              <item>
                               <spacer name="horizontalSpacer_2">
                                <property name="orientation">
                                 <enum>Qt::Horizontal</enum>
                                </property>
                                <property name="sizeHint" stdset="0">
                                 <size>
                                  <width>40</width>
                                  <height>20</height>
                                 </size>
                                </property>
                               </spacer>
                              </item>
                             </layout>
                            </item>
                           </layout>
                          </widget>
                         </item>
                        </layout>
                       </item>
                       <item>
                        <widget class="QGroupBox" name="groupBox_4">
                         <property name="maximumSize">
                          <size>
                           <width>16777215</width>
                           <height>60</height>
                          </size>
                         </property>
                         <property name="font">
                          <font>
                           <family>Microsoft YaHei</family>
                           <pointsize>8</pointsize>
                          </font>
                         </property>
                         <property name="title">
                          <string>数据</string>
                         </property>
                         <layout class="QGridLayout" name="gridLayout_9">
                          <item row="0" column="0">
                           <layout class="QHBoxLayout" name="horizontalLayout_7">
                            <item>
                             <widget class="QLabel" name="label_3">
                              <property name="font">
                               <font>
                                <family>Microsoft YaHei</family>
                                <pointsize>12</pointsize>
                               </font>
                              </property>
                              <property name="text">
                               <string>选取LED：</string>
                              </property>
                             </widget>
                            </item>
                            <item>
                             <widget class="QComboBox" name="comboBox_ledData">
                              <property name="minimumSize">
                               <size>
                                <width>130</width>
                                <height>0</height>
                               </size>
                              </property>
                             </widget>
                            </item>
                            <item>
                             <widget class="QPushButton" name="pushButton_watch_led">
                              <property name="text">
                               <string>查看数据</string>
                              </property>
                             </widget>
                            </item>
                           </layout>
                          </item>
                         </layout>
                        </widget>
                       </item>
                       <item>
                        <widget class="QGroupBox" name="groupBox_7">
                         <property name="font">
                          <font>
                           <family>Microsoft YaHei</family>
                           <pointsize>8</pointsize>
                          </font>
                         </property>
                         <property name="title">
                          <string>校准</string>
                         </property>
                         <layout class="QVBoxLayout" name="verticalLayout_43">
                          <property name="topMargin">
                           <number>20</number>
                          </property>
                          <item>
                           <layout class="QHBoxLayout" name="horizontalLayout_38">
                            <item>
                             <widget class="QLabel" name="label_23">
                              <property name="font">
                               <font>
                                <family>Microsoft YaHei</family>
                                <pointsize>12</pointsize>
                               </font>
                              </property>
                              <property name="text">
                               <string>通道:</string>
                              </property>
                             </widget>
                            </item>
                            <item>
                             <widget class="QLineEdit" name="lineEdit_channels">
                              <property name="text">
                               <string>1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29</string>
                              </property>
                              <property name="readOnly">
                               <bool>false</bool>
                              </property>
                              <property name="placeholderText">
                               <string>0~13,注意英文间隔</string>
                              </property>
                             </widget>
                            </item>
                           </layout>
                          </item>
                          <item>
                           <layout class="QHBoxLayout" name="horizontalLayout_41">
                            <item>
                             <widget class="QLabel" name="label_28">
                              <property name="font">
                               <font>
                                <family>Microsoft YaHei</family>
                                <pointsize>12</pointsize>
                               </font>
                              </property>
                              <property name="text">
                               <string>节点:</string>
                              </property>
                             </widget>
                            </item>
                            <item>
                             <widget class="QLineEdit" name="lineEdit_notes">
                              <property name="font">
                               <font>
                                <family>Microsoft YaHei</family>
                                <pointsize>12</pointsize>
                               </font>
                              </property>
                              <property name="text">
                               <string>1000,950,900,850,800,750,700,650,600,550,500,450,400,350,300,250,200,150,100,50,40,30,20,10</string>
                              </property>
                              <property name="readOnly">
                               <bool>false</bool>
                              </property>
                              <property name="placeholderText">
                               <string>0~1000,注意英文间隔</string>
                              </property>
                             </widget>
                            </item>
                           </layout>
                          </item>
                          <item>
                           <layout class="QHBoxLayout" name="horizontalLayout_44">
                            <item>
                             <widget class="QLabel" name="label_29">
                              <property name="font">
                               <font>
                                <family>Microsoft YaHei</family>
                                <pointsize>12</pointsize>
                               </font>
                              </property>
                              <property name="text">
                               <string>保存:</string>
                              </property>
                             </widget>
                            </item>
                            <item>
                             <widget class="QLineEdit" name="lineEdit_correctionSavePath"/>
                            </item>
                            <item>
                             <widget class="QToolButton" name="toolButton_imporTCorectionSavePath">
                              <property name="font">
                               <font>
                                <family>Microsoft YaHei</family>
                                <pointsize>12</pointsize>
                               </font>
                              </property>
                              <property name="text">
                               <string>...</string>
                              </property>
                             </widget>
                            </item>
                           </layout>
                          </item>
                          <item>
                           <layout class="QHBoxLayout" name="horizontalLayout_14">
                            <item>
                             <spacer name="horizontalSpacer_7">
                              <property name="orientation">
                               <enum>Qt::Horizontal</enum>
                              </property>
                              <property name="sizeHint" stdset="0">
                               <size>
                                <width>40</width>
                                <height>20</height>
                               </size>
                              </property>
                             </spacer>
                            </item>
                            <item>
                             <widget class="QPushButton" name="pushButton_startCorrection">
                              <property name="text">
                               <string>开始校准</string>
                              </property>
                             </widget>
                            </item>
                            <item>
                             <widget class="QPushButton" name="pushButton_stopCorrection">
                              <property name="text">
                               <string>停止校准</string>
                              </property>
                             </widget>
                            </item>
                           </layout>
                          </item>
                          <item>
                           <widget class="QProgressBar" name="progressBar">
                            <property name="value">
                             <number>0</number>
                            </property>
                           </widget>
                          </item>
                          <item>
                           <spacer name="verticalSpacer_3">
                            <property name="orientation">
                             <enum>Qt::Vertical</enum>
                            </property>
                            <property name="sizeHint" stdset="0">
                             <size>
                              <width>20</width>
                              <height>40</height>
                             </size>
                            </property>
                           </spacer>
                          </item>
                         </layout>
                        </widget>
                       </item>
                      </layout>
                     </widget>
                    </widget>
                   </item>
                  </layout>
                 </widget>
                 <widget class="QWidget" name="tab_2">
                  <attribute name="title">
                   <string>  光谱匹配  </string>
                  </attribute>
                  <layout class="QVBoxLayout" name="verticalLayout_44">
                   <property name="spacing">
                    <number>0</number>
                   </property>
                   <property name="leftMargin">
                    <number>0</number>
                   </property>
                   <property name="topMargin">
                    <number>0</number>
                   </property>
                   <property name="rightMargin">
                    <number>0</number>
                   </property>
                   <property name="bottomMargin">
                    <number>0</number>
                   </property>
                   <item>
                    <widget class="QGroupBox" name="groupBox_6">
                     <property name="title">
                      <string/>
                     </property>
                     <layout class="QGridLayout" name="gridLayout_2">
                      <item row="0" column="0">
                       <layout class="QHBoxLayout" name="horizontalLayout_6">
                        <item>
                         <widget class="QLabel" name="label_17">
                          <property name="maximumSize">
                           <size>
                            <width>16777215</width>
                            <height>20</height>
                           </size>
                          </property>
                          <property name="font">
                           <font>
                            <family>Microsoft YaHei</family>
                            <pointsize>12</pointsize>
                           </font>
                          </property>
                          <property name="text">
                           <string>匹配数据:</string>
                          </property>
                         </widget>
                        </item>
                        <item>
                         <spacer name="horizontalSpacer_46">
                          <property name="orientation">
                           <enum>Qt::Horizontal</enum>
                          </property>
                          <property name="sizeHint" stdset="0">
                           <size>
                            <width>97</width>
                            <height>27</height>
                           </size>
                          </property>
                         </spacer>
                        </item>
                       </layout>
                      </item>
                      <item row="1" column="0">
                       <layout class="QHBoxLayout" name="horizontalLayout_27">
                        <item>
                         <widget class="QCheckBox" name="checkBox_ch1">
                          <property name="enabled">
                           <bool>true</bool>
                          </property>
                          <property name="text">
                           <string>1</string>
                          </property>
                          <property name="checked">
                           <bool>true</bool>
                          </property>
                         </widget>
                        </item>
                        <item>
                         <widget class="QCheckBox" name="checkBox_ch2">
                          <property name="text">
                           <string>2</string>
                          </property>
                         </widget>
                        </item>
                        <item>
                         <widget class="QCheckBox" name="checkBox_ch3">
                          <property name="text">
                           <string>3</string>
                          </property>
                         </widget>
                        </item>
                        <item>
                         <widget class="QCheckBox" name="checkBox_ch4">
                          <property name="text">
                           <string>4</string>
                          </property>
                         </widget>
                        </item>
                        <item>
                         <widget class="QCheckBox" name="checkBox_ch5">
                          <property name="text">
                           <string>5</string>
                          </property>
                         </widget>
                        </item>
                        <item>
                         <widget class="QCheckBox" name="checkBox_ch6">
                          <property name="text">
                           <string>6</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </item>
                      <item row="2" column="0">
                       <layout class="QHBoxLayout" name="horizontalLayout_28">
                        <item>
                         <widget class="QRadioButton" name="radioButton_MatchSpdA">
                          <property name="minimumSize">
                           <size>
                            <width>55</width>
                            <height>0</height>
                           </size>
                          </property>
                          <property name="font">
                           <font>
                            <family>Microsoft YaHei</family>
                            <pointsize>12</pointsize>
                           </font>
                          </property>
                          <property name="text">
                           <string>A</string>
                          </property>
                          <property name="checked">
                           <bool>true</bool>
                          </property>
                          <attribute name="buttonGroup">
                           <string notr="true">buttonGroup_choose_spd</string>
                          </attribute>
                         </widget>
                        </item>
                        <item>
                         <widget class="QRadioButton" name="radioButton_MatchSpdD50">
                          <property name="minimumSize">
                           <size>
                            <width>70</width>
                            <height>0</height>
                           </size>
                          </property>
                          <property name="font">
                           <font>
                            <family>Microsoft YaHei</family>
                            <pointsize>12</pointsize>
                           </font>
                          </property>
                          <property name="text">
                           <string>D50</string>
                          </property>
                          <attribute name="buttonGroup">
                           <string notr="true">buttonGroup_choose_spd</string>
                          </attribute>
                         </widget>
                        </item>
                        <item>
                         <widget class="QRadioButton" name="radioButton_MatchSpdD55">
                          <property name="minimumSize">
                           <size>
                            <width>70</width>
                            <height>0</height>
                           </size>
                          </property>
                          <property name="font">
                           <font>
                            <family>Microsoft YaHei</family>
                            <pointsize>12</pointsize>
                           </font>
                          </property>
                          <property name="text">
                           <string>D55</string>
                          </property>
                          <attribute name="buttonGroup">
                           <string notr="true">buttonGroup_choose_spd</string>
                          </attribute>
                         </widget>
                        </item>
                        <item>
                         <widget class="QRadioButton" name="radioButton_MatchSpdD65">
                          <property name="minimumSize">
                           <size>
                            <width>70</width>
                            <height>0</height>
                           </size>
                          </property>
                          <property name="font">
                           <font>
                            <family>Microsoft YaHei</family>
                            <pointsize>12</pointsize>
                           </font>
                          </property>
                          <property name="text">
                           <string>D65</string>
                          </property>
                          <attribute name="buttonGroup">
                           <string notr="true">buttonGroup_choose_spd</string>
                          </attribute>
                         </widget>
                        </item>
                        <item>
                         <widget class="QRadioButton" name="radioButton_MatchSpdD75">
                          <property name="minimumSize">
                           <size>
                            <width>70</width>
                            <height>0</height>
                           </size>
                          </property>
                          <property name="font">
                           <font>
                            <family>Microsoft YaHei</family>
                            <pointsize>12</pointsize>
                           </font>
                          </property>
                          <property name="text">
                           <string>D75</string>
                          </property>
                          <attribute name="buttonGroup">
                           <string notr="true">buttonGroup_choose_spd</string>
                          </attribute>
                         </widget>
                        </item>
                       </layout>
                      </item>
                      <item row="3" column="0">
                       <layout class="QHBoxLayout" name="horizontalLayout_29">
                        <item>
                         <widget class="QRadioButton" name="radioButton_MatchSpdCWF">
                          <property name="minimumSize">
                           <size>
                            <width>75</width>
                            <height>0</height>
                           </size>
                          </property>
                          <property name="font">
                           <font>
                            <family>Microsoft YaHei</family>
                            <pointsize>12</pointsize>
                           </font>
                          </property>
                          <property name="text">
                           <string>CWF</string>
                          </property>
                          <attribute name="buttonGroup">
                           <string notr="true">buttonGroup_choose_spd</string>
                          </attribute>
                         </widget>
                        </item>
                        <item>
                         <widget class="QRadioButton" name="radioButton_MatchSpdTL84">
                          <property name="minimumSize">
                           <size>
                            <width>75</width>
                            <height>0</height>
                           </size>
                          </property>
                          <property name="font">
                           <font>
                            <family>Microsoft YaHei</family>
                            <pointsize>12</pointsize>
                           </font>
                          </property>
                          <property name="text">
                           <string>TL84</string>
                          </property>
                          <attribute name="buttonGroup">
                           <string notr="true">buttonGroup_choose_spd</string>
                          </attribute>
                         </widget>
                        </item>
                        <item>
                         <widget class="QRadioButton" name="radioButton_MatchSpdTL83">
                          <property name="minimumSize">
                           <size>
                            <width>75</width>
                            <height>0</height>
                           </size>
                          </property>
                          <property name="font">
                           <font>
                            <family>Microsoft YaHei</family>
                            <pointsize>12</pointsize>
                           </font>
                          </property>
                          <property name="text">
                           <string>TL83</string>
                          </property>
                          <attribute name="buttonGroup">
                           <string notr="true">buttonGroup_choose_spd</string>
                          </attribute>
                         </widget>
                        </item>
                        <item>
                         <widget class="QRadioButton" name="radioButton_MatchSpd">
                          <property name="minimumSize">
                           <size>
                            <width>70</width>
                            <height>0</height>
                           </size>
                          </property>
                          <property name="font">
                           <font>
                            <family>Microsoft YaHei</family>
                            <pointsize>12</pointsize>
                           </font>
                          </property>
                          <property name="text">
                           <string>H</string>
                          </property>
                          <attribute name="buttonGroup">
                           <string notr="true">buttonGroup_choose_spd</string>
                          </attribute>
                         </widget>
                        </item>
                        <item>
                         <widget class="QRadioButton" name="radioButton_MatchSpdOther">
                          <property name="minimumSize">
                           <size>
                            <width>70</width>
                            <height>0</height>
                           </size>
                          </property>
                          <property name="font">
                           <font>
                            <family>Microsoft YaHei</family>
                            <pointsize>12</pointsize>
                           </font>
                          </property>
                          <property name="text">
                           <string>Other</string>
                          </property>
                          <attribute name="buttonGroup">
                           <string notr="true">buttonGroup_choose_spd</string>
                          </attribute>
                         </widget>
                        </item>
                       </layout>
                      </item>
                      <item row="4" column="0">
                       <layout class="QHBoxLayout" name="horizontalLayout_26">
                        <item>
                         <widget class="QLineEdit" name="lineEdit_MatchSpdPath">
                          <property name="enabled">
                           <bool>false</bool>
                          </property>
                         </widget>
                        </item>
                        <item>
                         <widget class="QPushButton" name="pushButton_MatchSpdImport">
                          <property name="enabled">
                           <bool>false</bool>
                          </property>
                          <property name="text">
                           <string>导入</string>
                          </property>
                         </widget>
                        </item>
                        <item>
                         <widget class="QLabel" name="label_31">
                          <property name="text">
                           <string>/</string>
                          </property>
                         </widget>
                        </item>
                        <item>
                         <widget class="QPushButton" name="pushButton_MatchSpdMeasure">
                          <property name="enabled">
                           <bool>false</bool>
                          </property>
                          <property name="text">
                           <string>测量</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </item>
                      <item row="5" column="0">
                       <layout class="QHBoxLayout" name="horizontalLayout_45">
                        <item>
                         <widget class="QLabel" name="label_53">
                          <property name="font">
                           <font>
                            <family>Microsoft YaHei</family>
                            <pointsize>12</pointsize>
                           </font>
                          </property>
                          <property name="text">
                           <string>匹配方式：</string>
                          </property>
                         </widget>
                        </item>
                        <item>
                         <widget class="QComboBox" name="comboBox_MatchMethod">
                          <property name="currentIndex">
                           <number>1</number>
                          </property>
                          <item>
                           <property name="text">
                            <string>power</string>
                           </property>
                          </item>
                          <item>
                           <property name="text">
                            <string>scale</string>
                           </property>
                          </item>
                         </widget>
                        </item>
                        <item>
                         <widget class="QLabel" name="label_18">
                          <property name="font">
                           <font>
                            <family>Microsoft YaHei</family>
                            <pointsize>12</pointsize>
                           </font>
                          </property>
                          <property name="text">
                           <string>光谱精度:</string>
                          </property>
                         </widget>
                        </item>
                        <item>
                         <widget class="QSpinBox" name="spinBox_SpdAccuracy">
                          <property name="layoutDirection">
                           <enum>Qt::LeftToRight</enum>
                          </property>
                          <property name="buttonSymbols">
                           <enum>QAbstractSpinBox::UpDownArrows</enum>
                          </property>
                          <property name="specialValueText">
                           <string/>
                          </property>
                          <property name="suffix">
                           <string>nm</string>
                          </property>
                          <property name="prefix">
                           <string/>
                          </property>
                          <property name="minimum">
                           <number>1</number>
                          </property>
                          <property name="maximum">
                           <number>10</number>
                          </property>
                         </widget>
                        </item>
                        <item>
                         <spacer name="horizontalSpacer_52">
                          <property name="orientation">
                           <enum>Qt::Horizontal</enum>
                          </property>
                          <property name="sizeHint" stdset="0">
                           <size>
                            <width>31</width>
                            <height>11</height>
                           </size>
                          </property>
                         </spacer>
                        </item>
                       </layout>
                      </item>
                      <item row="6" column="0">
                       <layout class="QHBoxLayout" name="horizontalLayout_39">
                        <item>
                         <widget class="QLabel" name="label_30">
                          <property name="font">
                           <font>
                            <family>Microsoft YaHei</family>
                            <pointsize>12</pointsize>
                           </font>
                          </property>
                          <property name="text">
                           <string>目标亮度(lx/m2):</string>
                          </property>
                         </widget>
                        </item>
                        <item>
                         <widget class="QSpinBox" name="spinBox_tagetLuminance">
                          <property name="layoutDirection">
                           <enum>Qt::LeftToRight</enum>
                          </property>
                          <property name="buttonSymbols">
                           <enum>QAbstractSpinBox::UpDownArrows</enum>
                          </property>
                          <property name="specialValueText">
                           <string/>
                          </property>
                          <property name="suffix">
                           <string/>
                          </property>
                          <property name="prefix">
                           <string/>
                          </property>
                          <property name="maximum">
                           <number>100000</number>
                          </property>
                         </widget>
                        </item>
                        <item>
                         <spacer name="horizontalSpacer_43">
                          <property name="orientation">
                           <enum>Qt::Horizontal</enum>
                          </property>
                          <property name="sizeHint" stdset="0">
                           <size>
                            <width>100</width>
                            <height>11</height>
                           </size>
                          </property>
                         </spacer>
                        </item>
                       </layout>
                      </item>
                      <item row="7" column="0">
                       <layout class="QHBoxLayout" name="horizontalLayout_40">
                        <item>
                         <widget class="QLabel" name="label_24">
                          <property name="font">
                           <font>
                            <family>Microsoft YaHei</family>
                            <pointsize>12</pointsize>
                           </font>
                          </property>
                          <property name="text">
                           <string>实时调控:</string>
                          </property>
                         </widget>
                        </item>
                        <item>
                         <widget class="QRadioButton" name="radioButton_send_true">
                          <property name="font">
                           <font>
                            <family>Microsoft YaHei</family>
                            <pointsize>12</pointsize>
                           </font>
                          </property>
                          <property name="text">
                           <string>是</string>
                          </property>
                          <property name="icon">
                           <iconset>
                            <normaloff>C:/Users/<USER>/.designer/icon.ico</normaloff>C:/Users/<USER>/.designer/icon.ico</iconset>
                          </property>
                          <property name="checked">
                           <bool>true</bool>
                          </property>
                         </widget>
                        </item>
                        <item>
                         <widget class="QRadioButton" name="radioButton_send_false">
                          <property name="font">
                           <font>
                            <family>Microsoft YaHei</family>
                            <pointsize>12</pointsize>
                           </font>
                          </property>
                          <property name="text">
                           <string>否</string>
                          </property>
                         </widget>
                        </item>
                        <item>
                         <widget class="QPushButton" name="pushButton_matchStart">
                          <property name="minimumSize">
                           <size>
                            <width>100</width>
                            <height>0</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>开始匹配</string>
                          </property>
                         </widget>
                        </item>
                        <item>
                         <widget class="QPushButton" name="pushButton_correction">
                          <property name="minimumSize">
                           <size>
                            <width>100</width>
                            <height>0</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>矫正</string>
                          </property>
                         </widget>
                        </item>
                        <item>
                         <widget class="QPushButton" name="pushButton_clear_spec">
                          <property name="text">
                           <string>重置</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </item>
                      <item row="8" column="0">
                       <widget class="QGroupBox" name="groupBox_3">
                        <property name="minimumSize">
                         <size>
                          <width>0</width>
                          <height>100</height>
                         </size>
                        </property>
                        <property name="title">
                         <string>TL84</string>
                        </property>
                        <layout class="QGridLayout" name="gridLayout_10">
                         <item row="0" column="0">
                          <layout class="QHBoxLayout" name="horizontalLayout_10">
                           <item>
                            <widget class="QPushButton" name="tl84_switch_pushButton">
                             <property name="text">
                              <string>打开</string>
                             </property>
                            </widget>
                           </item>
                           <item>
                            <widget class="QSpinBox" name="tl84_spinBox">
                             <property name="maximum">
                              <number>1500</number>
                             </property>
                             <property name="singleStep">
                              <number>100</number>
                             </property>
                             <property name="value">
                              <number>500</number>
                             </property>
                            </widget>
                           </item>
                           <item>
                            <widget class="QPushButton" name="tl84_send_pushButton">
                             <property name="text">
                              <string>设置</string>
                             </property>
                            </widget>
                           </item>
                          </layout>
                         </item>
                        </layout>
                       </widget>
                      </item>
                      <item row="9" column="0">
                       <widget class="QGroupBox" name="groupBox_8">
                        <property name="minimumSize">
                         <size>
                          <width>0</width>
                          <height>100</height>
                         </size>
                        </property>
                        <property name="title">
                         <string>CWF</string>
                        </property>
                        <layout class="QGridLayout" name="gridLayout_11">
                         <item row="0" column="0">
                          <layout class="QHBoxLayout" name="horizontalLayout_11">
                           <item>
                            <widget class="QPushButton" name="cwf_switch_pushButton">
                             <property name="text">
                              <string>打开</string>
                             </property>
                            </widget>
                           </item>
                           <item>
                            <widget class="QSpinBox" name="cwf_spinBox">
                             <property name="maximum">
                              <number>1050</number>
                             </property>
                             <property name="singleStep">
                              <number>100</number>
                             </property>
                             <property name="value">
                              <number>500</number>
                             </property>
                            </widget>
                           </item>
                           <item>
                            <widget class="QPushButton" name="cwf_send_pushButton">
                             <property name="text">
                              <string>设置</string>
                             </property>
                            </widget>
                           </item>
                          </layout>
                         </item>
                        </layout>
                       </widget>
                      </item>
                      <item row="10" column="0">
                       <spacer name="verticalSpacer_2">
                        <property name="orientation">
                         <enum>Qt::Vertical</enum>
                        </property>
                        <property name="sizeHint" stdset="0">
                         <size>
                          <width>20</width>
                          <height>30</height>
                         </size>
                        </property>
                       </spacer>
                      </item>
                     </layout>
                    </widget>
                   </item>
                  </layout>
                 </widget>
                 <widget class="QWidget" name="tab_3">
                  <attribute name="title">
                   <string>  保存指令  </string>
                  </attribute>
                  <layout class="QGridLayout" name="gridLayout">
                   <item row="0" column="0">
                    <widget class="QTableWidget" name="tableWidget">
                     <property name="font">
                      <font>
                       <family>Microsoft YaHei</family>
                       <pointsize>12</pointsize>
                      </font>
                     </property>
                    </widget>
                   </item>
                   <item row="1" column="0">
                    <layout class="QHBoxLayout" name="horizontalLayout_8">
                     <item>
                      <widget class="QPushButton" name="pushButton_importOrder_2">
                       <property name="text">
                        <string>导入指令</string>
                       </property>
                      </widget>
                     </item>
                     <item>
                      <widget class="QPushButton" name="pushButton_saveOrder">
                       <property name="text">
                        <string>保存指令</string>
                       </property>
                      </widget>
                     </item>
                     <item>
                      <widget class="QPushButton" name="pushButton_sendLight">
                       <property name="text">
                        <string>设置光源</string>
                       </property>
                      </widget>
                     </item>
                     <item>
                      <widget class="QPushButton" name="pushButton_saveOrder_to_light">
                       <property name="text">
                        <string>写入光源</string>
                       </property>
                      </widget>
                     </item>
                    </layout>
                   </item>
                  </layout>
                 </widget>
                 <widget class="QWidget" name="tab_5">
                  <attribute name="title">
                   <string>场景</string>
                  </attribute>
                  <layout class="QGridLayout" name="gridLayout_12">
                   <item row="0" column="0">
                    <widget class="QTableWidget" name="tableWidget_loop"/>
                   </item>
                   <item row="1" column="0">
                    <layout class="QHBoxLayout" name="horizontalLayout_13">
                     <item>
                      <widget class="QPushButton" name="pushButton_loop_save">
                       <property name="text">
                        <string>保存</string>
                       </property>
                      </widget>
                     </item>
                     <item>
                      <widget class="QPushButton" name="pushButton_loop_run">
                       <property name="text">
                        <string>运行</string>
                       </property>
                      </widget>
                     </item>
                    </layout>
                   </item>
                  </layout>
                 </widget>
                 <widget class="QWidget" name="tab_4">
                  <attribute name="title">
                   <string> 闪烁  </string>
                  </attribute>
                  <layout class="QVBoxLayout" name="verticalLayout_46">
                   <item>
                    <layout class="QGridLayout" name="gridLayout_4">
                     <item row="1" column="3">
                      <widget class="QPushButton" name="pushButton_spec_status">
                       <property name="maximumSize">
                        <size>
                         <width>20</width>
                         <height>99999</height>
                        </size>
                       </property>
                       <property name="text">
                        <string/>
                       </property>
                      </widget>
                     </item>
                     <item row="0" column="0">
                      <widget class="QLabel" name="label_5">
                       <property name="font">
                        <font>
                         <family>Microsoft YaHei</family>
                         <pointsize>12</pointsize>
                        </font>
                       </property>
                       <property name="text">
                        <string>频率：</string>
                       </property>
                      </widget>
                     </item>
                     <item row="0" column="2">
                      <widget class="QPushButton" name="pushButton_sendflickLight">
                       <property name="text">
                        <string>设置光源</string>
                       </property>
                      </widget>
                     </item>
                     <item row="1" column="0">
                      <widget class="QLabel" name="label_6">
                       <property name="font">
                        <font>
                         <family>Microsoft YaHei</family>
                         <pointsize>12</pointsize>
                        </font>
                       </property>
                       <property name="text">
                        <string>端口：</string>
                       </property>
                      </widget>
                     </item>
                     <item row="0" column="3">
                      <spacer name="horizontalSpacer_5">
                       <property name="orientation">
                        <enum>Qt::Horizontal</enum>
                       </property>
                       <property name="sizeHint" stdset="0">
                        <size>
                         <width>40</width>
                         <height>20</height>
                        </size>
                       </property>
                      </spacer>
                     </item>
                     <item row="0" column="1">
                      <widget class="QSpinBox" name="flick_spinBox">
                       <property name="font">
                        <font>
                         <family>Microsoft YaHei</family>
                         <pointsize>12</pointsize>
                        </font>
                       </property>
                       <property name="maximum">
                        <number>50000</number>
                       </property>
                       <property name="singleStep">
                        <number>100</number>
                       </property>
                       <property name="value">
                        <number>1000</number>
                       </property>
                      </widget>
                     </item>
                     <item row="1" column="2">
                      <widget class="QPushButton" name="spec_serials_connect">
                       <property name="maximumSize">
                        <size>
                         <width>16777215</width>
                         <height>99999</height>
                        </size>
                       </property>
                       <property name="text">
                        <string>连接</string>
                       </property>
                      </widget>
                     </item>
                     <item row="1" column="1">
                      <widget class="QComboBox" name="spec_comboBox_serials">
                       <property name="minimumSize">
                        <size>
                         <width>120</width>
                         <height>0</height>
                        </size>
                       </property>
                       <property name="maximumSize">
                        <size>
                         <width>80</width>
                         <height>16777215</height>
                        </size>
                       </property>
                       <property name="font">
                        <font>
                         <family>Microsoft YaHei</family>
                         <pointsize>12</pointsize>
                        </font>
                       </property>
                      </widget>
                     </item>
                     <item row="2" column="0">
                      <spacer name="verticalSpacer">
                       <property name="orientation">
                        <enum>Qt::Vertical</enum>
                       </property>
                       <property name="sizeHint" stdset="0">
                        <size>
                         <width>20</width>
                         <height>40</height>
                        </size>
                       </property>
                      </spacer>
                     </item>
                    </layout>
                   </item>
                  </layout>
                 </widget>
                </widget>
               </item>
              </layout>
             </widget>
            </item>
            <item>
             <widget class="QGroupBox" name="groupBox_spd_plot">
              <property name="minimumSize">
               <size>
                <width>600</width>
                <height>0</height>
               </size>
              </property>
              <property name="title">
               <string>光谱显示</string>
              </property>
             </widget>
            </item>
            <item>
             <layout class="QVBoxLayout" name="verticalLayout_3">
              <item>
               <widget class="QGroupBox" name="groupBox_5">
                <property name="minimumSize">
                 <size>
                  <width>400</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <family>Microsoft YaHei</family>
                  <pointsize>8</pointsize>
                 </font>
                </property>
                <property name="title">
                 <string>测量-500A</string>
                </property>
                <layout class="QGridLayout" name="gridLayout_6">
                 <item row="7" column="1">
                  <widget class="QLineEdit" name="lineEdit_Measure_Re_1_to_5">
                   <property name="font">
                    <font>
                     <family>Microsoft YaHei</family>
                     <pointsize>10</pointsize>
                    </font>
                   </property>
                   <property name="styleSheet">
                    <string notr="true">font-size:10pt;</string>
                   </property>
                   <property name="text">
                    <string/>
                   </property>
                  </widget>
                 </item>
                 <item row="7" column="0">
                  <widget class="QLineEdit" name="lineEdit_Measure_Re_2">
                   <property name="maximumSize">
                    <size>
                     <width>100</width>
                     <height>16777215</height>
                    </size>
                   </property>
                   <property name="text">
                    <string>R1-R5</string>
                   </property>
                  </widget>
                 </item>
                 <item row="6" column="0" colspan="2">
                  <layout class="QHBoxLayout" name="horizontalLayout_55" stretch="0,0,0,0">
                   <item>
                    <widget class="QComboBox" name="comboBox_Measure_Re">
                     <property name="maximumSize">
                      <size>
                       <width>80</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="layoutDirection">
                      <enum>Qt::LeftToRight</enum>
                     </property>
                     <property name="currentIndex">
                      <number>0</number>
                     </property>
                     <item>
                      <property name="text">
                       <string>Ra</string>
                      </property>
                     </item>
                     <item>
                      <property name="text">
                       <string>R1</string>
                      </property>
                     </item>
                     <item>
                      <property name="text">
                       <string>R2</string>
                      </property>
                     </item>
                     <item>
                      <property name="text">
                       <string>R3</string>
                      </property>
                     </item>
                     <item>
                      <property name="text">
                       <string>R4</string>
                      </property>
                     </item>
                     <item>
                      <property name="text">
                       <string>R5</string>
                      </property>
                     </item>
                     <item>
                      <property name="text">
                       <string>R6</string>
                      </property>
                     </item>
                     <item>
                      <property name="text">
                       <string>R7</string>
                      </property>
                     </item>
                     <item>
                      <property name="text">
                       <string>R8</string>
                      </property>
                     </item>
                     <item>
                      <property name="text">
                       <string>R9</string>
                      </property>
                     </item>
                     <item>
                      <property name="text">
                       <string>R10</string>
                      </property>
                     </item>
                     <item>
                      <property name="text">
                       <string>R11</string>
                      </property>
                     </item>
                     <item>
                      <property name="text">
                       <string>R12</string>
                      </property>
                     </item>
                     <item>
                      <property name="text">
                       <string>R13</string>
                      </property>
                     </item>
                     <item>
                      <property name="text">
                       <string>R14</string>
                      </property>
                     </item>
                     <item>
                      <property name="text">
                       <string>R15</string>
                      </property>
                     </item>
                    </widget>
                   </item>
                   <item>
                    <widget class="QLineEdit" name="lineEdit_Measure_Re">
                     <property name="maximumSize">
                      <size>
                       <width>80</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="text">
                      <string/>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <spacer name="horizontalSpacer_45">
                     <property name="orientation">
                      <enum>Qt::Horizontal</enum>
                     </property>
                     <property name="sizeHint" stdset="0">
                      <size>
                       <width>129</width>
                       <height>28</height>
                      </size>
                     </property>
                    </spacer>
                   </item>
                   <item>
                    <widget class="QPushButton" name="pushButton_measure500A">
                     <property name="text">
                      <string>测量</string>
                     </property>
                    </widget>
                   </item>
                  </layout>
                 </item>
                 <item row="1" column="0" colspan="2">
                  <layout class="QHBoxLayout" name="horizontalLayout_47">
                   <property name="spacing">
                    <number>6</number>
                   </property>
                   <property name="leftMargin">
                    <number>0</number>
                   </property>
                   <property name="topMargin">
                    <number>0</number>
                   </property>
                   <property name="rightMargin">
                    <number>0</number>
                   </property>
                   <property name="bottomMargin">
                    <number>0</number>
                   </property>
                   <item>
                    <widget class="QLabel" name="label_35">
                     <property name="enabled">
                      <bool>true</bool>
                     </property>
                     <property name="font">
                      <font>
                       <family>Microsoft YaHei</family>
                       <pointsize>12</pointsize>
                      </font>
                     </property>
                     <property name="text">
                      <string>Ev</string>
                     </property>
                     <property name="alignment">
                      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <widget class="QLineEdit" name="lineEdit_Measure_Ev">
                     <property name="maximumSize">
                      <size>
                       <width>9999</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="layoutDirection">
                      <enum>Qt::LeftToRight</enum>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">font-size:10pt;padding:0 0;</string>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <widget class="QLabel" name="label_37">
                     <property name="enabled">
                      <bool>true</bool>
                     </property>
                     <property name="font">
                      <font>
                       <family>Microsoft YaHei</family>
                       <pointsize>12</pointsize>
                      </font>
                     </property>
                     <property name="text">
                      <string>x</string>
                     </property>
                     <property name="alignment">
                      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <widget class="QLineEdit" name="lineEdit_Measure_x">
                     <property name="maximumSize">
                      <size>
                       <width>9999</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">font-size:10pt;padding:0 0;</string>
                     </property>
                     <property name="text">
                      <string/>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <widget class="QLabel" name="label_38">
                     <property name="enabled">
                      <bool>true</bool>
                     </property>
                     <property name="font">
                      <font>
                       <family>Microsoft YaHei</family>
                       <pointsize>12</pointsize>
                      </font>
                     </property>
                     <property name="text">
                      <string>y</string>
                     </property>
                     <property name="alignment">
                      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <widget class="QLineEdit" name="lineEdit_Measure_y">
                     <property name="maximumSize">
                      <size>
                       <width>9999</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">font-size:10pt;padding:0 0;</string>
                     </property>
                     <property name="text">
                      <string/>
                     </property>
                    </widget>
                   </item>
                  </layout>
                 </item>
                 <item row="5" column="0" colspan="2">
                  <layout class="QHBoxLayout" name="horizontalLayout_54">
                   <item>
                    <widget class="QLabel" name="label_50">
                     <property name="enabled">
                      <bool>true</bool>
                     </property>
                     <property name="font">
                      <font>
                       <family>Microsoft YaHei</family>
                       <pointsize>12</pointsize>
                      </font>
                     </property>
                     <property name="text">
                      <string>DW</string>
                     </property>
                     <property name="alignment">
                      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <widget class="QLineEdit" name="lineEdit_Measure_DW">
                     <property name="maximumSize">
                      <size>
                       <width>9999</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">font-size:10pt;padding:0 0;</string>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <widget class="QLabel" name="label_51">
                     <property name="enabled">
                      <bool>true</bool>
                     </property>
                     <property name="font">
                      <font>
                       <family>Microsoft YaHei</family>
                       <pointsize>12</pointsize>
                      </font>
                     </property>
                     <property name="text">
                      <string>Pe</string>
                     </property>
                     <property name="alignment">
                      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <widget class="QLineEdit" name="lineEdit_Measure_Pe">
                     <property name="maximumSize">
                      <size>
                       <width>9999</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">font-size:10pt;padding:0 0;</string>
                     </property>
                     <property name="text">
                      <string/>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <widget class="QLabel" name="label_52">
                     <property name="enabled">
                      <bool>true</bool>
                     </property>
                     <property name="font">
                      <font>
                       <family>Microsoft YaHei</family>
                       <pointsize>12</pointsize>
                      </font>
                     </property>
                     <property name="text">
                      <string>Es</string>
                     </property>
                     <property name="alignment">
                      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <widget class="QLineEdit" name="lineEdit_Measure_Es">
                     <property name="maximumSize">
                      <size>
                       <width>9999</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">font-size:10pt;padding:0 0;</string>
                     </property>
                    </widget>
                   </item>
                  </layout>
                 </item>
                 <item row="4" column="0" colspan="2">
                  <layout class="QHBoxLayout" name="horizontalLayout_53">
                   <item>
                    <widget class="QLabel" name="label_47">
                     <property name="enabled">
                      <bool>true</bool>
                     </property>
                     <property name="font">
                      <font>
                       <family>Microsoft YaHei</family>
                       <pointsize>12</pointsize>
                      </font>
                     </property>
                     <property name="text">
                      <string>duv</string>
                     </property>
                     <property name="alignment">
                      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <widget class="QLineEdit" name="lineEdit_Measure_duv">
                     <property name="maximumSize">
                      <size>
                       <width>9999</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">font-size:10pt;padding:0 0;</string>
                     </property>
                     <property name="text">
                      <string/>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <widget class="QLabel" name="label_48">
                     <property name="enabled">
                      <bool>true</bool>
                     </property>
                     <property name="font">
                      <font>
                       <family>Microsoft YaHei</family>
                       <pointsize>12</pointsize>
                      </font>
                     </property>
                     <property name="text">
                      <string>PW</string>
                     </property>
                     <property name="alignment">
                      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <widget class="QLineEdit" name="lineEdit_Measure_PW">
                     <property name="maximumSize">
                      <size>
                       <width>9999</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">font-size:10pt;padding:0 0;</string>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <widget class="QLabel" name="label_49">
                     <property name="enabled">
                      <bool>true</bool>
                     </property>
                     <property name="font">
                      <font>
                       <family>Microsoft YaHei</family>
                       <pointsize>12</pointsize>
                      </font>
                     </property>
                     <property name="text">
                      <string>SP</string>
                     </property>
                     <property name="alignment">
                      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <widget class="QLineEdit" name="lineEdit_Measure_SP">
                     <property name="maximumSize">
                      <size>
                       <width>9999</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">font-size:10pt;padding:0 0;</string>
                     </property>
                    </widget>
                   </item>
                  </layout>
                 </item>
                 <item row="2" column="0" colspan="2">
                  <layout class="QHBoxLayout" name="horizontalLayout_49">
                   <item>
                    <widget class="QLabel" name="label_39">
                     <property name="enabled">
                      <bool>true</bool>
                     </property>
                     <property name="font">
                      <font>
                       <family>Microsoft YaHei</family>
                       <pointsize>12</pointsize>
                      </font>
                     </property>
                     <property name="text">
                      <string>T</string>
                     </property>
                     <property name="alignment">
                      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <widget class="QLineEdit" name="lineEdit_Measure_T">
                     <property name="maximumSize">
                      <size>
                       <width>9999</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">font-size:10pt;padding:0 0;</string>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <widget class="QLabel" name="label_42">
                     <property name="enabled">
                      <bool>true</bool>
                     </property>
                     <property name="font">
                      <font>
                       <family>Microsoft YaHei</family>
                       <pointsize>12</pointsize>
                      </font>
                     </property>
                     <property name="text">
                      <string>u'</string>
                     </property>
                     <property name="alignment">
                      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <widget class="QLineEdit" name="lineEdit_Measure_u">
                     <property name="maximumSize">
                      <size>
                       <width>9999</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">font-size:10pt;padding:0 0;</string>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <widget class="QLabel" name="label_43">
                     <property name="enabled">
                      <bool>true</bool>
                     </property>
                     <property name="font">
                      <font>
                       <family>Microsoft YaHei</family>
                       <pointsize>12</pointsize>
                      </font>
                     </property>
                     <property name="text">
                      <string>v'</string>
                     </property>
                     <property name="alignment">
                      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <widget class="QLineEdit" name="lineEdit_Measure_v">
                     <property name="maximumSize">
                      <size>
                       <width>9999</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">font-size:10pt;padding:0 0;</string>
                     </property>
                     <property name="text">
                      <string/>
                     </property>
                    </widget>
                   </item>
                  </layout>
                 </item>
                 <item row="0" column="0">
                  <widget class="QLabel" name="label_7">
                   <property name="text">
                    <string/>
                   </property>
                  </widget>
                 </item>
                 <item row="3" column="0" colspan="2">
                  <layout class="QHBoxLayout" name="horizontalLayout_50">
                   <item>
                    <widget class="QLabel" name="label_44">
                     <property name="enabled">
                      <bool>true</bool>
                     </property>
                     <property name="font">
                      <font>
                       <family>Microsoft YaHei</family>
                       <pointsize>12</pointsize>
                      </font>
                     </property>
                     <property name="text">
                      <string>X</string>
                     </property>
                     <property name="alignment">
                      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <widget class="QLineEdit" name="lineEdit_Measure_X">
                     <property name="maximumSize">
                      <size>
                       <width>9999</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">font-size:10pt;padding:0 0;</string>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <widget class="QLabel" name="label_45">
                     <property name="enabled">
                      <bool>true</bool>
                     </property>
                     <property name="font">
                      <font>
                       <family>Microsoft YaHei</family>
                       <pointsize>12</pointsize>
                      </font>
                     </property>
                     <property name="text">
                      <string>Y</string>
                     </property>
                     <property name="alignment">
                      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <widget class="QLineEdit" name="lineEdit_Measure_Y">
                     <property name="maximumSize">
                      <size>
                       <width>9999</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">font-size:10pt;padding:0 0;</string>
                     </property>
                     <property name="text">
                      <string/>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <widget class="QLabel" name="label_46">
                     <property name="enabled">
                      <bool>true</bool>
                     </property>
                     <property name="font">
                      <font>
                       <family>Microsoft YaHei</family>
                       <pointsize>12</pointsize>
                      </font>
                     </property>
                     <property name="text">
                      <string>Z</string>
                     </property>
                     <property name="alignment">
                      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <widget class="QLineEdit" name="lineEdit_Measure_Z">
                     <property name="maximumSize">
                      <size>
                       <width>9999</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">font-size:10pt;padding:0 0;</string>
                     </property>
                     <property name="text">
                      <string/>
                     </property>
                    </widget>
                   </item>
                  </layout>
                 </item>
                 <item row="8" column="0">
                  <widget class="QLineEdit" name="lineEdit">
                   <property name="maximumSize">
                    <size>
                     <width>100</width>
                     <height>16777215</height>
                    </size>
                   </property>
                   <property name="text">
                    <string>R6-R10</string>
                   </property>
                  </widget>
                 </item>
                 <item row="8" column="1">
                  <widget class="QLineEdit" name="lineEdit_Measure_Re_6_to_10">
                   <property name="font">
                    <font>
                     <family>Microsoft YaHei</family>
                     <pointsize>10</pointsize>
                    </font>
                   </property>
                   <property name="styleSheet">
                    <string notr="true">font-size:10pt;</string>
                   </property>
                  </widget>
                 </item>
                 <item row="9" column="0">
                  <widget class="QLineEdit" name="lineEdit_39">
                   <property name="maximumSize">
                    <size>
                     <width>100</width>
                     <height>16777215</height>
                    </size>
                   </property>
                   <property name="text">
                    <string>R11-R15</string>
                   </property>
                  </widget>
                 </item>
                 <item row="9" column="1">
                  <widget class="QLineEdit" name="lineEdit_Measure_Re_11_to_15">
                   <property name="font">
                    <font>
                     <family>Microsoft YaHei</family>
                     <pointsize>10</pointsize>
                    </font>
                   </property>
                   <property name="styleSheet">
                    <string notr="true">font-size:10pt;</string>
                   </property>
                  </widget>
                 </item>
                </layout>
               </widget>
              </item>
              <item>
               <widget class="QTextBrowser" name="textBrowser_message">
                <property name="font">
                 <font>
                  <family>Microsoft YaHei</family>
                  <pointsize>12</pointsize>
                 </font>
                </property>
               </widget>
              </item>
             </layout>
            </item>
           </layout>
          </item>
         </layout>
        </item>
        <item row="1" column="0">
         <widget class="QScrollArea" name="scrollArea">
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>300</height>
           </size>
          </property>
          <property name="widgetResizable">
           <bool>true</bool>
          </property>
          <widget class="QWidget" name="scrollAreaWidgetContents">
           <property name="geometry">
            <rect>
             <x>0</x>
             <y>0</y>
             <width>2853</width>
             <height>243</height>
            </rect>
           </property>
           <layout class="QHBoxLayout" name="horizontalLayout_3">
            <property name="spacing">
             <number>0</number>
            </property>
            <property name="leftMargin">
             <number>0</number>
            </property>
            <property name="topMargin">
             <number>0</number>
            </property>
            <property name="rightMargin">
             <number>0</number>
            </property>
            <property name="bottomMargin">
             <number>0</number>
            </property>
            <item>
             <widget class="QGroupBox" name="groupBox_2">
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>250</height>
               </size>
              </property>
              <property name="title">
               <string/>
              </property>
              <layout class="QHBoxLayout" name="horizontalLayout_9">
               <property name="spacing">
                <number>0</number>
               </property>
               <property name="leftMargin">
                <number>0</number>
               </property>
               <property name="topMargin">
                <number>0</number>
               </property>
               <property name="rightMargin">
                <number>0</number>
               </property>
               <property name="bottomMargin">
                <number>0</number>
               </property>
               <item>
                <widget class="QWidget" name="widget_1" native="true">
                 <layout class="QVBoxLayout" name="verticalLayout_4">
                  <property name="spacing">
                   <number>0</number>
                  </property>
                  <property name="leftMargin">
                   <number>0</number>
                  </property>
                  <property name="topMargin">
                   <number>0</number>
                  </property>
                  <property name="rightMargin">
                   <number>0</number>
                  </property>
                  <property name="bottomMargin">
                   <number>0</number>
                  </property>
                  <item>
                   <layout class="QGridLayout" name="gridLayout_21">
                    <item row="2" column="0" colspan="3">
                     <widget class="QLineEdit" name="lineEdit_1">
                      <property name="minimumSize">
                       <size>
                        <width>75</width>
                        <height>21</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>9999</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="text">
                       <string>0.0</string>
                      </property>
                      <property name="maxLength">
                       <number>4</number>
                      </property>
                     </widget>
                    </item>
                    <item row="0" column="0" colspan="3">
                     <widget class="QCheckBox" name="checkBox_1">
                      <property name="minimumSize">
                       <size>
                        <width>50</width>
                        <height>20</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>99999</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <family>Microsoft YaHei</family>
                        <pointsize>12</pointsize>
                       </font>
                      </property>
                      <property name="styleSheet">
                       <string notr="true">background-color: rgb(50, 57, 255);
color: rgb(250, 255, 255);
border:1px solid white;</string>
                      </property>
                      <property name="text">
                       <string>1</string>
                      </property>
                      <property name="checked">
                       <bool>true</bool>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="0">
                     <spacer name="horizontalSpacer_41">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                    <item row="1" column="1">
                     <widget class="QSlider" name="verticalSlider_1">
                      <property name="minimumSize">
                       <size>
                        <width>16</width>
                        <height>151</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>16</width>
                        <height>151</height>
                       </size>
                      </property>
                      <property name="maximum">
                       <number>1000</number>
                      </property>
                      <property name="orientation">
                       <enum>Qt::Vertical</enum>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="2">
                     <spacer name="horizontalSpacer_42">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                   </layout>
                  </item>
                 </layout>
                </widget>
               </item>
               <item>
                <widget class="QWidget" name="widget_2" native="true">
                 <layout class="QVBoxLayout" name="verticalLayout_5">
                  <property name="spacing">
                   <number>0</number>
                  </property>
                  <property name="leftMargin">
                   <number>0</number>
                  </property>
                  <property name="topMargin">
                   <number>0</number>
                  </property>
                  <property name="rightMargin">
                   <number>0</number>
                  </property>
                  <property name="bottomMargin">
                   <number>0</number>
                  </property>
                  <item>
                   <layout class="QGridLayout" name="gridLayout_31">
                    <item row="2" column="0" colspan="3">
                     <widget class="QLineEdit" name="lineEdit_2">
                      <property name="minimumSize">
                       <size>
                        <width>75</width>
                        <height>21</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>9999</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="text">
                       <string>0.0</string>
                      </property>
                      <property name="maxLength">
                       <number>4</number>
                      </property>
                     </widget>
                    </item>
                    <item row="0" column="0" colspan="3">
                     <widget class="QCheckBox" name="checkBox_2">
                      <property name="minimumSize">
                       <size>
                        <width>50</width>
                        <height>20</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>99999</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <family>Microsoft YaHei</family>
                        <pointsize>12</pointsize>
                       </font>
                      </property>
                      <property name="styleSheet">
                       <string notr="true">background-color: rgb(85, 85, 255);
color: rgb(250, 255, 255);border:1px solid white;</string>
                      </property>
                      <property name="text">
                       <string>2</string>
                      </property>
                      <property name="checked">
                       <bool>true</bool>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="0">
                     <spacer name="horizontalSpacer_61">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                    <item row="1" column="1">
                     <widget class="QSlider" name="verticalSlider_2">
                      <property name="minimumSize">
                       <size>
                        <width>16</width>
                        <height>151</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>16</width>
                        <height>151</height>
                       </size>
                      </property>
                      <property name="maximum">
                       <number>1000</number>
                      </property>
                      <property name="orientation">
                       <enum>Qt::Vertical</enum>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="2">
                     <spacer name="horizontalSpacer_62">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                   </layout>
                  </item>
                 </layout>
                </widget>
               </item>
               <item>
                <widget class="QWidget" name="widget_3" native="true">
                 <layout class="QVBoxLayout" name="verticalLayout_6">
                  <property name="spacing">
                   <number>0</number>
                  </property>
                  <property name="leftMargin">
                   <number>0</number>
                  </property>
                  <property name="topMargin">
                   <number>0</number>
                  </property>
                  <property name="rightMargin">
                   <number>0</number>
                  </property>
                  <property name="bottomMargin">
                   <number>0</number>
                  </property>
                  <item>
                   <layout class="QGridLayout" name="gridLayout_32">
                    <item row="2" column="0" colspan="3">
                     <widget class="QLineEdit" name="lineEdit_3">
                      <property name="minimumSize">
                       <size>
                        <width>75</width>
                        <height>21</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>9999</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="text">
                       <string>0.0</string>
                      </property>
                      <property name="maxLength">
                       <number>4</number>
                      </property>
                     </widget>
                    </item>
                    <item row="0" column="0" colspan="3">
                     <widget class="QCheckBox" name="checkBox_3">
                      <property name="minimumSize">
                       <size>
                        <width>50</width>
                        <height>20</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>99999</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <family>Microsoft YaHei</family>
                        <pointsize>12</pointsize>
                       </font>
                      </property>
                      <property name="styleSheet">
                       <string notr="true">background-color: rgb(50, 57, 255);
color: rgb(250, 255, 255);border:1px solid white;</string>
                      </property>
                      <property name="text">
                       <string>3</string>
                      </property>
                      <property name="checked">
                       <bool>true</bool>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="0">
                     <spacer name="horizontalSpacer_63">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                    <item row="1" column="1">
                     <widget class="QSlider" name="verticalSlider_3">
                      <property name="minimumSize">
                       <size>
                        <width>16</width>
                        <height>151</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>16</width>
                        <height>151</height>
                       </size>
                      </property>
                      <property name="maximum">
                       <number>1000</number>
                      </property>
                      <property name="orientation">
                       <enum>Qt::Vertical</enum>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="2">
                     <spacer name="horizontalSpacer_64">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                   </layout>
                  </item>
                 </layout>
                </widget>
               </item>
               <item>
                <widget class="QWidget" name="widget_4" native="true">
                 <layout class="QVBoxLayout" name="verticalLayout_7">
                  <property name="spacing">
                   <number>0</number>
                  </property>
                  <property name="leftMargin">
                   <number>0</number>
                  </property>
                  <property name="topMargin">
                   <number>0</number>
                  </property>
                  <property name="rightMargin">
                   <number>0</number>
                  </property>
                  <property name="bottomMargin">
                   <number>0</number>
                  </property>
                  <item>
                   <layout class="QGridLayout" name="gridLayout_33">
                    <item row="2" column="0" colspan="3">
                     <widget class="QLineEdit" name="lineEdit_4">
                      <property name="minimumSize">
                       <size>
                        <width>75</width>
                        <height>21</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>9999</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="text">
                       <string>0.0</string>
                      </property>
                      <property name="maxLength">
                       <number>4</number>
                      </property>
                     </widget>
                    </item>
                    <item row="0" column="0" colspan="3">
                     <widget class="QCheckBox" name="checkBox_4">
                      <property name="minimumSize">
                       <size>
                        <width>50</width>
                        <height>20</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>99999</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <family>Microsoft YaHei</family>
                        <pointsize>12</pointsize>
                       </font>
                      </property>
                      <property name="styleSheet">
                       <string notr="true">background-color: rgb(50, 57, 255);
color: rgb(250, 255, 255);border:1px solid white;</string>
                      </property>
                      <property name="text">
                       <string>4</string>
                      </property>
                      <property name="checked">
                       <bool>true</bool>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="0">
                     <spacer name="horizontalSpacer_65">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                    <item row="1" column="1">
                     <widget class="QSlider" name="verticalSlider_4">
                      <property name="minimumSize">
                       <size>
                        <width>16</width>
                        <height>151</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>16</width>
                        <height>151</height>
                       </size>
                      </property>
                      <property name="maximum">
                       <number>1000</number>
                      </property>
                      <property name="orientation">
                       <enum>Qt::Vertical</enum>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="2">
                     <spacer name="horizontalSpacer_66">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                   </layout>
                  </item>
                 </layout>
                </widget>
               </item>
               <item>
                <widget class="QWidget" name="widget_5" native="true">
                 <layout class="QVBoxLayout" name="verticalLayout_8">
                  <property name="spacing">
                   <number>0</number>
                  </property>
                  <property name="leftMargin">
                   <number>0</number>
                  </property>
                  <property name="topMargin">
                   <number>0</number>
                  </property>
                  <property name="rightMargin">
                   <number>0</number>
                  </property>
                  <property name="bottomMargin">
                   <number>0</number>
                  </property>
                  <item>
                   <layout class="QGridLayout" name="gridLayout_34">
                    <item row="2" column="0" colspan="3">
                     <widget class="QLineEdit" name="lineEdit_5">
                      <property name="minimumSize">
                       <size>
                        <width>75</width>
                        <height>21</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>9999</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="text">
                       <string>0.0</string>
                      </property>
                      <property name="maxLength">
                       <number>4</number>
                      </property>
                     </widget>
                    </item>
                    <item row="0" column="0" colspan="3">
                     <widget class="QCheckBox" name="checkBox_5">
                      <property name="minimumSize">
                       <size>
                        <width>50</width>
                        <height>20</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>99999</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <family>Microsoft YaHei</family>
                        <pointsize>12</pointsize>
                       </font>
                      </property>
                      <property name="styleSheet">
                       <string notr="true">background-color: rgb(50, 57, 255);
color: rgb(250, 255, 255);border:1px solid white;</string>
                      </property>
                      <property name="text">
                       <string>5</string>
                      </property>
                      <property name="checked">
                       <bool>true</bool>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="0">
                     <spacer name="horizontalSpacer_67">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                    <item row="1" column="1">
                     <widget class="QSlider" name="verticalSlider_5">
                      <property name="minimumSize">
                       <size>
                        <width>16</width>
                        <height>151</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>16</width>
                        <height>151</height>
                       </size>
                      </property>
                      <property name="maximum">
                       <number>1000</number>
                      </property>
                      <property name="orientation">
                       <enum>Qt::Vertical</enum>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="2">
                     <spacer name="horizontalSpacer_68">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                   </layout>
                  </item>
                 </layout>
                </widget>
               </item>
               <item>
                <widget class="QWidget" name="widget_6" native="true">
                 <layout class="QVBoxLayout" name="verticalLayout_9">
                  <property name="spacing">
                   <number>0</number>
                  </property>
                  <property name="leftMargin">
                   <number>0</number>
                  </property>
                  <property name="topMargin">
                   <number>0</number>
                  </property>
                  <property name="rightMargin">
                   <number>0</number>
                  </property>
                  <property name="bottomMargin">
                   <number>0</number>
                  </property>
                  <item>
                   <layout class="QGridLayout" name="gridLayout_35">
                    <item row="2" column="0" colspan="3">
                     <widget class="QLineEdit" name="lineEdit_6">
                      <property name="minimumSize">
                       <size>
                        <width>75</width>
                        <height>21</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>9999</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="text">
                       <string>0.0</string>
                      </property>
                      <property name="maxLength">
                       <number>4</number>
                      </property>
                     </widget>
                    </item>
                    <item row="0" column="0" colspan="3">
                     <widget class="QCheckBox" name="checkBox_6">
                      <property name="minimumSize">
                       <size>
                        <width>50</width>
                        <height>20</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>99999</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <family>Microsoft YaHei</family>
                        <pointsize>12</pointsize>
                       </font>
                      </property>
                      <property name="styleSheet">
                       <string notr="true">background-color: rgb(50, 57, 255);
color: rgb(250, 255, 255);border:1px solid white;</string>
                      </property>
                      <property name="text">
                       <string>6</string>
                      </property>
                      <property name="checked">
                       <bool>true</bool>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="0">
                     <spacer name="horizontalSpacer_69">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                    <item row="1" column="1">
                     <widget class="QSlider" name="verticalSlider_6">
                      <property name="minimumSize">
                       <size>
                        <width>16</width>
                        <height>151</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>16</width>
                        <height>151</height>
                       </size>
                      </property>
                      <property name="maximum">
                       <number>1000</number>
                      </property>
                      <property name="orientation">
                       <enum>Qt::Vertical</enum>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="2">
                     <spacer name="horizontalSpacer_70">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                   </layout>
                  </item>
                 </layout>
                </widget>
               </item>
               <item>
                <widget class="QWidget" name="widget_7" native="true">
                 <layout class="QVBoxLayout" name="verticalLayout_10">
                  <property name="spacing">
                   <number>0</number>
                  </property>
                  <property name="leftMargin">
                   <number>0</number>
                  </property>
                  <property name="topMargin">
                   <number>0</number>
                  </property>
                  <property name="rightMargin">
                   <number>0</number>
                  </property>
                  <property name="bottomMargin">
                   <number>0</number>
                  </property>
                  <item>
                   <layout class="QGridLayout" name="gridLayout_36">
                    <item row="2" column="0" colspan="3">
                     <widget class="QLineEdit" name="lineEdit_7">
                      <property name="minimumSize">
                       <size>
                        <width>75</width>
                        <height>21</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>9999</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="text">
                       <string>0.0</string>
                      </property>
                      <property name="maxLength">
                       <number>4</number>
                      </property>
                     </widget>
                    </item>
                    <item row="0" column="0" colspan="3">
                     <widget class="QCheckBox" name="checkBox_7">
                      <property name="minimumSize">
                       <size>
                        <width>50</width>
                        <height>20</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>99999</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <family>Microsoft YaHei</family>
                        <pointsize>12</pointsize>
                       </font>
                      </property>
                      <property name="styleSheet">
                       <string notr="true">background-color: rgb(50, 57, 255);
color: rgb(250, 255, 255);border:1px solid white;</string>
                      </property>
                      <property name="text">
                       <string>7</string>
                      </property>
                      <property name="checked">
                       <bool>true</bool>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="0">
                     <spacer name="horizontalSpacer_71">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                    <item row="1" column="1">
                     <widget class="QSlider" name="verticalSlider_7">
                      <property name="minimumSize">
                       <size>
                        <width>16</width>
                        <height>151</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>16</width>
                        <height>151</height>
                       </size>
                      </property>
                      <property name="maximum">
                       <number>1000</number>
                      </property>
                      <property name="orientation">
                       <enum>Qt::Vertical</enum>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="2">
                     <spacer name="horizontalSpacer_72">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                   </layout>
                  </item>
                 </layout>
                </widget>
               </item>
               <item>
                <widget class="QWidget" name="widget_8" native="true">
                 <layout class="QVBoxLayout" name="verticalLayout_11">
                  <property name="spacing">
                   <number>0</number>
                  </property>
                  <property name="leftMargin">
                   <number>0</number>
                  </property>
                  <property name="topMargin">
                   <number>0</number>
                  </property>
                  <property name="rightMargin">
                   <number>0</number>
                  </property>
                  <property name="bottomMargin">
                   <number>0</number>
                  </property>
                  <item>
                   <layout class="QGridLayout" name="gridLayout_37">
                    <item row="2" column="0" colspan="3">
                     <widget class="QLineEdit" name="lineEdit_8">
                      <property name="minimumSize">
                       <size>
                        <width>75</width>
                        <height>21</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>9999</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="text">
                       <string>0.0</string>
                      </property>
                      <property name="maxLength">
                       <number>4</number>
                      </property>
                     </widget>
                    </item>
                    <item row="0" column="0" colspan="3">
                     <widget class="QCheckBox" name="checkBox_8">
                      <property name="minimumSize">
                       <size>
                        <width>50</width>
                        <height>20</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>99999</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <family>Microsoft YaHei</family>
                        <pointsize>12</pointsize>
                       </font>
                      </property>
                      <property name="styleSheet">
                       <string notr="true">background-color: rgb(50, 57, 255);
color: rgb(250, 255, 255);border:1px solid white;</string>
                      </property>
                      <property name="text">
                       <string>8</string>
                      </property>
                      <property name="checked">
                       <bool>true</bool>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="0">
                     <spacer name="horizontalSpacer_73">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                    <item row="1" column="1">
                     <widget class="QSlider" name="verticalSlider_8">
                      <property name="minimumSize">
                       <size>
                        <width>16</width>
                        <height>151</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>16</width>
                        <height>151</height>
                       </size>
                      </property>
                      <property name="maximum">
                       <number>1000</number>
                      </property>
                      <property name="orientation">
                       <enum>Qt::Vertical</enum>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="2">
                     <spacer name="horizontalSpacer_74">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                   </layout>
                  </item>
                 </layout>
                </widget>
               </item>
               <item>
                <widget class="QWidget" name="widget_9" native="true">
                 <layout class="QVBoxLayout" name="verticalLayout_12">
                  <property name="spacing">
                   <number>0</number>
                  </property>
                  <property name="leftMargin">
                   <number>0</number>
                  </property>
                  <property name="topMargin">
                   <number>0</number>
                  </property>
                  <property name="rightMargin">
                   <number>0</number>
                  </property>
                  <property name="bottomMargin">
                   <number>0</number>
                  </property>
                  <item>
                   <layout class="QGridLayout" name="gridLayout_38">
                    <item row="2" column="0" colspan="3">
                     <widget class="QLineEdit" name="lineEdit_9">
                      <property name="minimumSize">
                       <size>
                        <width>75</width>
                        <height>21</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>9999</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="text">
                       <string>0.0</string>
                      </property>
                      <property name="maxLength">
                       <number>4</number>
                      </property>
                     </widget>
                    </item>
                    <item row="0" column="0" colspan="3">
                     <widget class="QCheckBox" name="checkBox_9">
                      <property name="minimumSize">
                       <size>
                        <width>50</width>
                        <height>20</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>99999</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <family>Microsoft YaHei</family>
                        <pointsize>12</pointsize>
                       </font>
                      </property>
                      <property name="styleSheet">
                       <string notr="true">background-color: rgb(50, 57, 255);
color: rgb(250, 255, 255);border:1px solid white;</string>
                      </property>
                      <property name="text">
                       <string>9</string>
                      </property>
                      <property name="checked">
                       <bool>true</bool>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="0">
                     <spacer name="horizontalSpacer_75">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                    <item row="1" column="1">
                     <widget class="QSlider" name="verticalSlider_9">
                      <property name="minimumSize">
                       <size>
                        <width>16</width>
                        <height>151</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>16</width>
                        <height>151</height>
                       </size>
                      </property>
                      <property name="maximum">
                       <number>1000</number>
                      </property>
                      <property name="orientation">
                       <enum>Qt::Vertical</enum>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="2">
                     <spacer name="horizontalSpacer_76">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                   </layout>
                  </item>
                 </layout>
                </widget>
               </item>
               <item>
                <widget class="QWidget" name="widget_10" native="true">
                 <layout class="QVBoxLayout" name="verticalLayout_13">
                  <property name="spacing">
                   <number>0</number>
                  </property>
                  <property name="leftMargin">
                   <number>0</number>
                  </property>
                  <property name="topMargin">
                   <number>0</number>
                  </property>
                  <property name="rightMargin">
                   <number>0</number>
                  </property>
                  <property name="bottomMargin">
                   <number>0</number>
                  </property>
                  <item>
                   <layout class="QGridLayout" name="gridLayout_39">
                    <item row="2" column="0" colspan="3">
                     <widget class="QLineEdit" name="lineEdit_10">
                      <property name="minimumSize">
                       <size>
                        <width>75</width>
                        <height>21</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>9999</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="text">
                       <string>0.0</string>
                      </property>
                      <property name="maxLength">
                       <number>4</number>
                      </property>
                     </widget>
                    </item>
                    <item row="0" column="0" colspan="3">
                     <widget class="QCheckBox" name="checkBox_10">
                      <property name="minimumSize">
                       <size>
                        <width>50</width>
                        <height>20</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>99999</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <family>Microsoft YaHei</family>
                        <pointsize>12</pointsize>
                       </font>
                      </property>
                      <property name="styleSheet">
                       <string notr="true">background-color: rgb(50, 57, 255);
color: rgb(250, 255, 255);border:1px solid white;</string>
                      </property>
                      <property name="text">
                       <string>10</string>
                      </property>
                      <property name="checked">
                       <bool>true</bool>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="0">
                     <spacer name="horizontalSpacer_77">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                    <item row="1" column="1">
                     <widget class="QSlider" name="verticalSlider_10">
                      <property name="minimumSize">
                       <size>
                        <width>16</width>
                        <height>151</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>16</width>
                        <height>151</height>
                       </size>
                      </property>
                      <property name="maximum">
                       <number>1000</number>
                      </property>
                      <property name="orientation">
                       <enum>Qt::Vertical</enum>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="2">
                     <spacer name="horizontalSpacer_78">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                   </layout>
                  </item>
                 </layout>
                </widget>
               </item>
               <item>
                <widget class="QWidget" name="widget_11" native="true">
                 <layout class="QVBoxLayout" name="verticalLayout_14">
                  <property name="spacing">
                   <number>0</number>
                  </property>
                  <property name="leftMargin">
                   <number>0</number>
                  </property>
                  <property name="topMargin">
                   <number>0</number>
                  </property>
                  <property name="rightMargin">
                   <number>0</number>
                  </property>
                  <property name="bottomMargin">
                   <number>0</number>
                  </property>
                  <item>
                   <layout class="QGridLayout" name="gridLayout_40">
                    <item row="2" column="0" colspan="3">
                     <widget class="QLineEdit" name="lineEdit_11">
                      <property name="minimumSize">
                       <size>
                        <width>75</width>
                        <height>21</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>9999</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="text">
                       <string>0.0</string>
                      </property>
                      <property name="maxLength">
                       <number>4</number>
                      </property>
                     </widget>
                    </item>
                    <item row="0" column="0" colspan="3">
                     <widget class="QCheckBox" name="checkBox_11">
                      <property name="minimumSize">
                       <size>
                        <width>50</width>
                        <height>20</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>99999</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <family>Microsoft YaHei</family>
                        <pointsize>12</pointsize>
                       </font>
                      </property>
                      <property name="styleSheet">
                       <string notr="true">background-color: rgb(50, 57, 255);
color: rgb(250, 255, 255);border:1px solid white;</string>
                      </property>
                      <property name="text">
                       <string>11</string>
                      </property>
                      <property name="checked">
                       <bool>true</bool>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="0">
                     <spacer name="horizontalSpacer_79">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                    <item row="1" column="1">
                     <widget class="QSlider" name="verticalSlider_11">
                      <property name="minimumSize">
                       <size>
                        <width>16</width>
                        <height>151</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>16</width>
                        <height>151</height>
                       </size>
                      </property>
                      <property name="maximum">
                       <number>1000</number>
                      </property>
                      <property name="orientation">
                       <enum>Qt::Vertical</enum>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="2">
                     <spacer name="horizontalSpacer_80">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                   </layout>
                  </item>
                 </layout>
                </widget>
               </item>
               <item>
                <widget class="QWidget" name="widget_12" native="true">
                 <layout class="QVBoxLayout" name="verticalLayout_15">
                  <property name="spacing">
                   <number>0</number>
                  </property>
                  <property name="leftMargin">
                   <number>0</number>
                  </property>
                  <property name="topMargin">
                   <number>0</number>
                  </property>
                  <property name="rightMargin">
                   <number>0</number>
                  </property>
                  <property name="bottomMargin">
                   <number>0</number>
                  </property>
                  <item>
                   <layout class="QGridLayout" name="gridLayout_41">
                    <item row="2" column="0" colspan="3">
                     <widget class="QLineEdit" name="lineEdit_12">
                      <property name="minimumSize">
                       <size>
                        <width>75</width>
                        <height>21</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>9999</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="text">
                       <string>0.0</string>
                      </property>
                      <property name="maxLength">
                       <number>4</number>
                      </property>
                     </widget>
                    </item>
                    <item row="0" column="0" colspan="3">
                     <widget class="QCheckBox" name="checkBox_12">
                      <property name="minimumSize">
                       <size>
                        <width>50</width>
                        <height>20</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>99999</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <family>Microsoft YaHei</family>
                        <pointsize>12</pointsize>
                       </font>
                      </property>
                      <property name="styleSheet">
                       <string notr="true">background-color: rgb(50, 57, 255);
color: rgb(250, 255, 255);border:1px solid white;</string>
                      </property>
                      <property name="text">
                       <string>12</string>
                      </property>
                      <property name="checked">
                       <bool>true</bool>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="0">
                     <spacer name="horizontalSpacer_81">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                    <item row="1" column="1">
                     <widget class="QSlider" name="verticalSlider_12">
                      <property name="minimumSize">
                       <size>
                        <width>16</width>
                        <height>151</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>16</width>
                        <height>151</height>
                       </size>
                      </property>
                      <property name="maximum">
                       <number>1000</number>
                      </property>
                      <property name="orientation">
                       <enum>Qt::Vertical</enum>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="2">
                     <spacer name="horizontalSpacer_82">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                   </layout>
                  </item>
                 </layout>
                </widget>
               </item>
               <item>
                <widget class="QWidget" name="widget_13" native="true">
                 <layout class="QVBoxLayout" name="verticalLayout_16">
                  <property name="spacing">
                   <number>0</number>
                  </property>
                  <property name="leftMargin">
                   <number>0</number>
                  </property>
                  <property name="topMargin">
                   <number>0</number>
                  </property>
                  <property name="rightMargin">
                   <number>0</number>
                  </property>
                  <property name="bottomMargin">
                   <number>0</number>
                  </property>
                  <item>
                   <layout class="QGridLayout" name="gridLayout_42">
                    <item row="2" column="0" colspan="3">
                     <widget class="QLineEdit" name="lineEdit_13">
                      <property name="minimumSize">
                       <size>
                        <width>75</width>
                        <height>21</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>9999</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="text">
                       <string>0.0</string>
                      </property>
                      <property name="maxLength">
                       <number>4</number>
                      </property>
                     </widget>
                    </item>
                    <item row="0" column="0" colspan="3">
                     <widget class="QCheckBox" name="checkBox_13">
                      <property name="minimumSize">
                       <size>
                        <width>50</width>
                        <height>20</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>99999</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <family>Microsoft YaHei</family>
                        <pointsize>12</pointsize>
                       </font>
                      </property>
                      <property name="styleSheet">
                       <string notr="true">background-color: rgb(50, 57, 255);
color: rgb(250, 255, 255);border:1px solid white;</string>
                      </property>
                      <property name="text">
                       <string>13</string>
                      </property>
                      <property name="checked">
                       <bool>true</bool>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="0">
                     <spacer name="horizontalSpacer_83">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                    <item row="1" column="1">
                     <widget class="QSlider" name="verticalSlider_13">
                      <property name="minimumSize">
                       <size>
                        <width>16</width>
                        <height>151</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>16</width>
                        <height>151</height>
                       </size>
                      </property>
                      <property name="maximum">
                       <number>1000</number>
                      </property>
                      <property name="orientation">
                       <enum>Qt::Vertical</enum>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="2">
                     <spacer name="horizontalSpacer_84">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                   </layout>
                  </item>
                 </layout>
                </widget>
               </item>
               <item>
                <widget class="QWidget" name="widget_14" native="true">
                 <layout class="QVBoxLayout" name="verticalLayout_17">
                  <property name="spacing">
                   <number>0</number>
                  </property>
                  <property name="leftMargin">
                   <number>0</number>
                  </property>
                  <property name="topMargin">
                   <number>0</number>
                  </property>
                  <property name="rightMargin">
                   <number>0</number>
                  </property>
                  <property name="bottomMargin">
                   <number>0</number>
                  </property>
                  <item>
                   <layout class="QGridLayout" name="gridLayout_43">
                    <item row="2" column="0" colspan="3">
                     <widget class="QLineEdit" name="lineEdit_14">
                      <property name="minimumSize">
                       <size>
                        <width>75</width>
                        <height>21</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>9999</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="text">
                       <string>0.0</string>
                      </property>
                      <property name="maxLength">
                       <number>4</number>
                      </property>
                     </widget>
                    </item>
                    <item row="0" column="0" colspan="3">
                     <widget class="QCheckBox" name="checkBox_14">
                      <property name="minimumSize">
                       <size>
                        <width>50</width>
                        <height>20</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>99999</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <family>Microsoft YaHei</family>
                        <pointsize>12</pointsize>
                       </font>
                      </property>
                      <property name="styleSheet">
                       <string notr="true">background-color: rgb(50, 57, 255);
color: rgb(250, 255, 255);border:1px solid white;</string>
                      </property>
                      <property name="text">
                       <string>14</string>
                      </property>
                      <property name="checked">
                       <bool>true</bool>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="0">
                     <spacer name="horizontalSpacer_85">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                    <item row="1" column="1">
                     <widget class="QSlider" name="verticalSlider_14">
                      <property name="minimumSize">
                       <size>
                        <width>16</width>
                        <height>151</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>16</width>
                        <height>151</height>
                       </size>
                      </property>
                      <property name="maximum">
                       <number>1000</number>
                      </property>
                      <property name="orientation">
                       <enum>Qt::Vertical</enum>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="2">
                     <spacer name="horizontalSpacer_86">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                   </layout>
                  </item>
                 </layout>
                </widget>
               </item>
               <item>
                <widget class="QWidget" name="widget_15" native="true">
                 <layout class="QVBoxLayout" name="verticalLayout_18">
                  <property name="spacing">
                   <number>0</number>
                  </property>
                  <property name="leftMargin">
                   <number>0</number>
                  </property>
                  <property name="topMargin">
                   <number>0</number>
                  </property>
                  <property name="rightMargin">
                   <number>0</number>
                  </property>
                  <property name="bottomMargin">
                   <number>0</number>
                  </property>
                  <item>
                   <layout class="QGridLayout" name="gridLayout_44">
                    <item row="2" column="0" colspan="3">
                     <widget class="QLineEdit" name="lineEdit_15">
                      <property name="minimumSize">
                       <size>
                        <width>75</width>
                        <height>21</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>9999</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="text">
                       <string>0.0</string>
                      </property>
                      <property name="maxLength">
                       <number>4</number>
                      </property>
                     </widget>
                    </item>
                    <item row="0" column="0" colspan="3">
                     <widget class="QCheckBox" name="checkBox_15">
                      <property name="minimumSize">
                       <size>
                        <width>50</width>
                        <height>20</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>99999</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <family>Microsoft YaHei</family>
                        <pointsize>12</pointsize>
                       </font>
                      </property>
                      <property name="styleSheet">
                       <string notr="true">background-color: rgb(50, 57, 255);
color: rgb(250, 255, 255);border:1px solid white;</string>
                      </property>
                      <property name="text">
                       <string>15</string>
                      </property>
                      <property name="checked">
                       <bool>true</bool>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="0">
                     <spacer name="horizontalSpacer_87">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                    <item row="1" column="1">
                     <widget class="QSlider" name="verticalSlider_15">
                      <property name="minimumSize">
                       <size>
                        <width>16</width>
                        <height>151</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>16</width>
                        <height>151</height>
                       </size>
                      </property>
                      <property name="maximum">
                       <number>1000</number>
                      </property>
                      <property name="orientation">
                       <enum>Qt::Vertical</enum>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="2">
                     <spacer name="horizontalSpacer_88">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                   </layout>
                  </item>
                 </layout>
                </widget>
               </item>
               <item>
                <widget class="QWidget" name="widget_16" native="true">
                 <layout class="QVBoxLayout" name="verticalLayout_19">
                  <property name="spacing">
                   <number>0</number>
                  </property>
                  <property name="leftMargin">
                   <number>0</number>
                  </property>
                  <property name="topMargin">
                   <number>0</number>
                  </property>
                  <property name="rightMargin">
                   <number>0</number>
                  </property>
                  <property name="bottomMargin">
                   <number>0</number>
                  </property>
                  <item>
                   <layout class="QGridLayout" name="gridLayout_45">
                    <item row="2" column="0" colspan="3">
                     <widget class="QLineEdit" name="lineEdit_16">
                      <property name="minimumSize">
                       <size>
                        <width>75</width>
                        <height>21</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>9999</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="text">
                       <string>0.0</string>
                      </property>
                      <property name="maxLength">
                       <number>4</number>
                      </property>
                     </widget>
                    </item>
                    <item row="0" column="0" colspan="3">
                     <widget class="QCheckBox" name="checkBox_16">
                      <property name="minimumSize">
                       <size>
                        <width>50</width>
                        <height>20</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>9999</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <family>Microsoft YaHei</family>
                        <pointsize>12</pointsize>
                       </font>
                      </property>
                      <property name="styleSheet">
                       <string notr="true">background-color: rgb(50, 57, 255);
color: rgb(250, 255, 255);border:1px solid white;</string>
                      </property>
                      <property name="text">
                       <string>16</string>
                      </property>
                      <property name="checked">
                       <bool>true</bool>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="0">
                     <spacer name="horizontalSpacer_89">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                    <item row="1" column="1">
                     <widget class="QSlider" name="verticalSlider_16">
                      <property name="minimumSize">
                       <size>
                        <width>16</width>
                        <height>151</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>16</width>
                        <height>151</height>
                       </size>
                      </property>
                      <property name="maximum">
                       <number>1000</number>
                      </property>
                      <property name="orientation">
                       <enum>Qt::Vertical</enum>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="2">
                     <spacer name="horizontalSpacer_90">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                   </layout>
                  </item>
                 </layout>
                </widget>
               </item>
               <item>
                <widget class="QWidget" name="widget_17" native="true">
                 <layout class="QVBoxLayout" name="verticalLayout_20">
                  <property name="spacing">
                   <number>0</number>
                  </property>
                  <property name="leftMargin">
                   <number>0</number>
                  </property>
                  <property name="topMargin">
                   <number>0</number>
                  </property>
                  <property name="rightMargin">
                   <number>0</number>
                  </property>
                  <property name="bottomMargin">
                   <number>0</number>
                  </property>
                  <item>
                   <layout class="QGridLayout" name="gridLayout_46">
                    <item row="2" column="0" colspan="3">
                     <widget class="QLineEdit" name="lineEdit_17">
                      <property name="minimumSize">
                       <size>
                        <width>75</width>
                        <height>21</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>9999</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="text">
                       <string>0.0</string>
                      </property>
                      <property name="maxLength">
                       <number>4</number>
                      </property>
                     </widget>
                    </item>
                    <item row="0" column="0" colspan="3">
                     <widget class="QCheckBox" name="checkBox_17">
                      <property name="minimumSize">
                       <size>
                        <width>50</width>
                        <height>20</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>99999</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <family>Microsoft YaHei</family>
                        <pointsize>12</pointsize>
                       </font>
                      </property>
                      <property name="styleSheet">
                       <string notr="true">background-color: rgb(50, 57, 255);
color: rgb(250, 255, 255);border:1px solid white;</string>
                      </property>
                      <property name="text">
                       <string>17</string>
                      </property>
                      <property name="checked">
                       <bool>true</bool>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="0">
                     <spacer name="horizontalSpacer_91">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                    <item row="1" column="1">
                     <widget class="QSlider" name="verticalSlider_17">
                      <property name="minimumSize">
                       <size>
                        <width>16</width>
                        <height>151</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>16</width>
                        <height>151</height>
                       </size>
                      </property>
                      <property name="maximum">
                       <number>1000</number>
                      </property>
                      <property name="orientation">
                       <enum>Qt::Vertical</enum>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="2">
                     <spacer name="horizontalSpacer_92">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                   </layout>
                  </item>
                 </layout>
                </widget>
               </item>
               <item>
                <widget class="QWidget" name="widget_18" native="true">
                 <layout class="QVBoxLayout" name="verticalLayout_21">
                  <property name="spacing">
                   <number>0</number>
                  </property>
                  <property name="leftMargin">
                   <number>0</number>
                  </property>
                  <property name="topMargin">
                   <number>0</number>
                  </property>
                  <property name="rightMargin">
                   <number>0</number>
                  </property>
                  <property name="bottomMargin">
                   <number>0</number>
                  </property>
                  <item>
                   <layout class="QGridLayout" name="gridLayout_47">
                    <item row="2" column="0" colspan="3">
                     <widget class="QLineEdit" name="lineEdit_18">
                      <property name="minimumSize">
                       <size>
                        <width>75</width>
                        <height>21</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>9999</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="text">
                       <string>0.0</string>
                      </property>
                      <property name="maxLength">
                       <number>4</number>
                      </property>
                     </widget>
                    </item>
                    <item row="0" column="0" colspan="3">
                     <widget class="QCheckBox" name="checkBox_18">
                      <property name="minimumSize">
                       <size>
                        <width>50</width>
                        <height>20</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>99999</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <family>Microsoft YaHei</family>
                        <pointsize>12</pointsize>
                       </font>
                      </property>
                      <property name="styleSheet">
                       <string notr="true">background-color: rgb(50, 57, 255);
color: rgb(250, 255, 255);border:1px solid white;</string>
                      </property>
                      <property name="text">
                       <string>18</string>
                      </property>
                      <property name="checked">
                       <bool>true</bool>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="0">
                     <spacer name="horizontalSpacer_93">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                    <item row="1" column="1">
                     <widget class="QSlider" name="verticalSlider_18">
                      <property name="minimumSize">
                       <size>
                        <width>16</width>
                        <height>151</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>16</width>
                        <height>151</height>
                       </size>
                      </property>
                      <property name="maximum">
                       <number>1000</number>
                      </property>
                      <property name="orientation">
                       <enum>Qt::Vertical</enum>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="2">
                     <spacer name="horizontalSpacer_94">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                   </layout>
                  </item>
                 </layout>
                </widget>
               </item>
               <item>
                <widget class="QWidget" name="widget_19" native="true">
                 <layout class="QVBoxLayout" name="verticalLayout_22">
                  <property name="spacing">
                   <number>0</number>
                  </property>
                  <property name="leftMargin">
                   <number>0</number>
                  </property>
                  <property name="topMargin">
                   <number>0</number>
                  </property>
                  <property name="rightMargin">
                   <number>0</number>
                  </property>
                  <property name="bottomMargin">
                   <number>0</number>
                  </property>
                  <item>
                   <layout class="QGridLayout" name="gridLayout_48">
                    <item row="2" column="0" colspan="3">
                     <widget class="QLineEdit" name="lineEdit_19">
                      <property name="minimumSize">
                       <size>
                        <width>75</width>
                        <height>21</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>9999</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="text">
                       <string>0.0</string>
                      </property>
                      <property name="maxLength">
                       <number>4</number>
                      </property>
                     </widget>
                    </item>
                    <item row="0" column="0" colspan="3">
                     <widget class="QCheckBox" name="checkBox_19">
                      <property name="minimumSize">
                       <size>
                        <width>50</width>
                        <height>20</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>99999</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <family>Microsoft YaHei</family>
                        <pointsize>12</pointsize>
                       </font>
                      </property>
                      <property name="styleSheet">
                       <string notr="true">background-color: rgb(50, 57, 255);
color: rgb(250, 255, 255);border:1px solid white;</string>
                      </property>
                      <property name="text">
                       <string>19</string>
                      </property>
                      <property name="checked">
                       <bool>true</bool>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="0">
                     <spacer name="horizontalSpacer_95">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                    <item row="1" column="1">
                     <widget class="QSlider" name="verticalSlider_19">
                      <property name="minimumSize">
                       <size>
                        <width>16</width>
                        <height>151</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>16</width>
                        <height>151</height>
                       </size>
                      </property>
                      <property name="maximum">
                       <number>1000</number>
                      </property>
                      <property name="orientation">
                       <enum>Qt::Vertical</enum>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="2">
                     <spacer name="horizontalSpacer_96">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                   </layout>
                  </item>
                 </layout>
                </widget>
               </item>
               <item>
                <widget class="QWidget" name="widget_20" native="true">
                 <layout class="QVBoxLayout" name="verticalLayout_23">
                  <property name="spacing">
                   <number>0</number>
                  </property>
                  <property name="leftMargin">
                   <number>0</number>
                  </property>
                  <property name="topMargin">
                   <number>0</number>
                  </property>
                  <property name="rightMargin">
                   <number>0</number>
                  </property>
                  <property name="bottomMargin">
                   <number>0</number>
                  </property>
                  <item>
                   <layout class="QGridLayout" name="gridLayout_49">
                    <item row="2" column="0" colspan="3">
                     <widget class="QLineEdit" name="lineEdit_20">
                      <property name="minimumSize">
                       <size>
                        <width>75</width>
                        <height>21</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>9999</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="text">
                       <string>0.0</string>
                      </property>
                      <property name="maxLength">
                       <number>4</number>
                      </property>
                     </widget>
                    </item>
                    <item row="0" column="0" colspan="3">
                     <widget class="QCheckBox" name="checkBox_20">
                      <property name="minimumSize">
                       <size>
                        <width>50</width>
                        <height>20</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>99999</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <family>Microsoft YaHei</family>
                        <pointsize>12</pointsize>
                       </font>
                      </property>
                      <property name="styleSheet">
                       <string notr="true">background-color: rgb(50, 57, 255);
color: rgb(250, 255, 255);border:1px solid white;</string>
                      </property>
                      <property name="text">
                       <string>20</string>
                      </property>
                      <property name="checked">
                       <bool>true</bool>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="0">
                     <spacer name="horizontalSpacer_97">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                    <item row="1" column="1">
                     <widget class="QSlider" name="verticalSlider_20">
                      <property name="minimumSize">
                       <size>
                        <width>16</width>
                        <height>151</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>16</width>
                        <height>151</height>
                       </size>
                      </property>
                      <property name="maximum">
                       <number>1000</number>
                      </property>
                      <property name="orientation">
                       <enum>Qt::Vertical</enum>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="2">
                     <spacer name="horizontalSpacer_98">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                   </layout>
                  </item>
                 </layout>
                </widget>
               </item>
               <item>
                <widget class="QWidget" name="widget_21" native="true">
                 <layout class="QVBoxLayout" name="verticalLayout_24">
                  <property name="spacing">
                   <number>0</number>
                  </property>
                  <property name="leftMargin">
                   <number>0</number>
                  </property>
                  <property name="topMargin">
                   <number>0</number>
                  </property>
                  <property name="rightMargin">
                   <number>0</number>
                  </property>
                  <property name="bottomMargin">
                   <number>0</number>
                  </property>
                  <item>
                   <layout class="QGridLayout" name="gridLayout_50">
                    <item row="2" column="0" colspan="3">
                     <widget class="QLineEdit" name="lineEdit_21">
                      <property name="minimumSize">
                       <size>
                        <width>75</width>
                        <height>21</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>9999</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="text">
                       <string>0.0</string>
                      </property>
                      <property name="maxLength">
                       <number>4</number>
                      </property>
                     </widget>
                    </item>
                    <item row="0" column="0" colspan="3">
                     <widget class="QCheckBox" name="checkBox_21">
                      <property name="minimumSize">
                       <size>
                        <width>50</width>
                        <height>20</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>99999</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <family>Microsoft YaHei</family>
                        <pointsize>12</pointsize>
                       </font>
                      </property>
                      <property name="styleSheet">
                       <string notr="true">background-color: rgb(50, 57, 255);
color: rgb(250, 255, 255);border:1px solid white;</string>
                      </property>
                      <property name="text">
                       <string>21</string>
                      </property>
                      <property name="checked">
                       <bool>true</bool>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="0">
                     <spacer name="horizontalSpacer_99">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                    <item row="1" column="1">
                     <widget class="QSlider" name="verticalSlider_21">
                      <property name="minimumSize">
                       <size>
                        <width>16</width>
                        <height>151</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>16</width>
                        <height>151</height>
                       </size>
                      </property>
                      <property name="maximum">
                       <number>1000</number>
                      </property>
                      <property name="orientation">
                       <enum>Qt::Vertical</enum>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="2">
                     <spacer name="horizontalSpacer_100">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                   </layout>
                  </item>
                 </layout>
                </widget>
               </item>
               <item>
                <widget class="QWidget" name="widget_22" native="true">
                 <layout class="QVBoxLayout" name="verticalLayout_25">
                  <property name="spacing">
                   <number>0</number>
                  </property>
                  <property name="leftMargin">
                   <number>0</number>
                  </property>
                  <property name="topMargin">
                   <number>0</number>
                  </property>
                  <property name="rightMargin">
                   <number>0</number>
                  </property>
                  <property name="bottomMargin">
                   <number>0</number>
                  </property>
                  <item>
                   <layout class="QGridLayout" name="gridLayout_51">
                    <item row="2" column="0" colspan="3">
                     <widget class="QLineEdit" name="lineEdit_22">
                      <property name="minimumSize">
                       <size>
                        <width>75</width>
                        <height>21</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>9999</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="text">
                       <string>0.0</string>
                      </property>
                      <property name="maxLength">
                       <number>4</number>
                      </property>
                     </widget>
                    </item>
                    <item row="0" column="0" colspan="3">
                     <widget class="QCheckBox" name="checkBox_22">
                      <property name="minimumSize">
                       <size>
                        <width>50</width>
                        <height>20</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>99999</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <family>Microsoft YaHei</family>
                        <pointsize>12</pointsize>
                       </font>
                      </property>
                      <property name="styleSheet">
                       <string notr="true">background-color: rgb(50, 57, 255);
color: rgb(250, 255, 255);border:1px solid white;</string>
                      </property>
                      <property name="text">
                       <string>22</string>
                      </property>
                      <property name="checked">
                       <bool>true</bool>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="0">
                     <spacer name="horizontalSpacer_101">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                    <item row="1" column="1">
                     <widget class="QSlider" name="verticalSlider_22">
                      <property name="minimumSize">
                       <size>
                        <width>16</width>
                        <height>151</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>16</width>
                        <height>151</height>
                       </size>
                      </property>
                      <property name="maximum">
                       <number>1000</number>
                      </property>
                      <property name="orientation">
                       <enum>Qt::Vertical</enum>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="2">
                     <spacer name="horizontalSpacer_102">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                   </layout>
                  </item>
                 </layout>
                </widget>
               </item>
               <item>
                <widget class="QWidget" name="widget_23" native="true">
                 <layout class="QVBoxLayout" name="verticalLayout_26">
                  <property name="spacing">
                   <number>0</number>
                  </property>
                  <property name="leftMargin">
                   <number>0</number>
                  </property>
                  <property name="topMargin">
                   <number>0</number>
                  </property>
                  <property name="rightMargin">
                   <number>0</number>
                  </property>
                  <property name="bottomMargin">
                   <number>0</number>
                  </property>
                  <item>
                   <layout class="QGridLayout" name="gridLayout_52">
                    <item row="2" column="0" colspan="3">
                     <widget class="QLineEdit" name="lineEdit_23">
                      <property name="minimumSize">
                       <size>
                        <width>75</width>
                        <height>21</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>9999</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="text">
                       <string>0.0</string>
                      </property>
                      <property name="maxLength">
                       <number>4</number>
                      </property>
                     </widget>
                    </item>
                    <item row="0" column="0" colspan="3">
                     <widget class="QCheckBox" name="checkBox_23">
                      <property name="minimumSize">
                       <size>
                        <width>50</width>
                        <height>20</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>99999</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <family>Microsoft YaHei</family>
                        <pointsize>12</pointsize>
                       </font>
                      </property>
                      <property name="styleSheet">
                       <string notr="true">background-color: rgb(50, 57, 255);
color: rgb(250, 255, 255);border:1px solid white;</string>
                      </property>
                      <property name="text">
                       <string>23</string>
                      </property>
                      <property name="checked">
                       <bool>true</bool>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="0">
                     <spacer name="horizontalSpacer_103">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                    <item row="1" column="1">
                     <widget class="QSlider" name="verticalSlider_23">
                      <property name="minimumSize">
                       <size>
                        <width>16</width>
                        <height>151</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>16</width>
                        <height>151</height>
                       </size>
                      </property>
                      <property name="maximum">
                       <number>1000</number>
                      </property>
                      <property name="orientation">
                       <enum>Qt::Vertical</enum>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="2">
                     <spacer name="horizontalSpacer_104">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                   </layout>
                  </item>
                 </layout>
                </widget>
               </item>
               <item>
                <widget class="QWidget" name="widget_24" native="true">
                 <layout class="QVBoxLayout" name="verticalLayout_27">
                  <property name="spacing">
                   <number>0</number>
                  </property>
                  <property name="leftMargin">
                   <number>0</number>
                  </property>
                  <property name="topMargin">
                   <number>0</number>
                  </property>
                  <property name="rightMargin">
                   <number>0</number>
                  </property>
                  <property name="bottomMargin">
                   <number>0</number>
                  </property>
                  <item>
                   <layout class="QGridLayout" name="gridLayout_53">
                    <item row="2" column="0" colspan="3">
                     <widget class="QLineEdit" name="lineEdit_24">
                      <property name="minimumSize">
                       <size>
                        <width>75</width>
                        <height>21</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>9999</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="text">
                       <string>0.0</string>
                      </property>
                      <property name="maxLength">
                       <number>4</number>
                      </property>
                     </widget>
                    </item>
                    <item row="0" column="0" colspan="3">
                     <widget class="QCheckBox" name="checkBox_24">
                      <property name="minimumSize">
                       <size>
                        <width>50</width>
                        <height>20</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>9999</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <family>Microsoft YaHei</family>
                        <pointsize>12</pointsize>
                       </font>
                      </property>
                      <property name="styleSheet">
                       <string notr="true">background-color: rgb(50, 57, 255);
color: rgb(250, 255, 255);border:1px solid white;</string>
                      </property>
                      <property name="text">
                       <string>24</string>
                      </property>
                      <property name="checked">
                       <bool>true</bool>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="0">
                     <spacer name="horizontalSpacer_105">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                    <item row="1" column="1">
                     <widget class="QSlider" name="verticalSlider_24">
                      <property name="minimumSize">
                       <size>
                        <width>16</width>
                        <height>151</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>16</width>
                        <height>151</height>
                       </size>
                      </property>
                      <property name="maximum">
                       <number>1000</number>
                      </property>
                      <property name="orientation">
                       <enum>Qt::Vertical</enum>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="2">
                     <spacer name="horizontalSpacer_106">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                   </layout>
                  </item>
                 </layout>
                </widget>
               </item>
               <item>
                <widget class="QWidget" name="widget_25" native="true">
                 <layout class="QVBoxLayout" name="verticalLayout_28">
                  <property name="spacing">
                   <number>0</number>
                  </property>
                  <property name="leftMargin">
                   <number>0</number>
                  </property>
                  <property name="topMargin">
                   <number>0</number>
                  </property>
                  <property name="rightMargin">
                   <number>0</number>
                  </property>
                  <property name="bottomMargin">
                   <number>0</number>
                  </property>
                  <item>
                   <layout class="QGridLayout" name="gridLayout_54">
                    <item row="2" column="0" colspan="3">
                     <widget class="QLineEdit" name="lineEdit_25">
                      <property name="minimumSize">
                       <size>
                        <width>75</width>
                        <height>21</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>9999</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="text">
                       <string>0.0</string>
                      </property>
                      <property name="maxLength">
                       <number>4</number>
                      </property>
                     </widget>
                    </item>
                    <item row="0" column="0" colspan="3">
                     <widget class="QCheckBox" name="checkBox_25">
                      <property name="minimumSize">
                       <size>
                        <width>50</width>
                        <height>20</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>9999</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <family>Microsoft YaHei</family>
                        <pointsize>12</pointsize>
                       </font>
                      </property>
                      <property name="styleSheet">
                       <string notr="true">background-color: rgb(50, 57, 255);
color: rgb(250, 255, 255);border:1px solid white;</string>
                      </property>
                      <property name="text">
                       <string>25</string>
                      </property>
                      <property name="checked">
                       <bool>true</bool>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="0">
                     <spacer name="horizontalSpacer_107">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                    <item row="1" column="1">
                     <widget class="QSlider" name="verticalSlider_25">
                      <property name="minimumSize">
                       <size>
                        <width>16</width>
                        <height>151</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>16</width>
                        <height>151</height>
                       </size>
                      </property>
                      <property name="maximum">
                       <number>1000</number>
                      </property>
                      <property name="orientation">
                       <enum>Qt::Vertical</enum>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="2">
                     <spacer name="horizontalSpacer_108">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                   </layout>
                  </item>
                 </layout>
                </widget>
               </item>
               <item>
                <widget class="QWidget" name="widget_26" native="true">
                 <layout class="QVBoxLayout" name="verticalLayout_29">
                  <property name="spacing">
                   <number>0</number>
                  </property>
                  <property name="leftMargin">
                   <number>0</number>
                  </property>
                  <property name="topMargin">
                   <number>0</number>
                  </property>
                  <property name="rightMargin">
                   <number>0</number>
                  </property>
                  <property name="bottomMargin">
                   <number>0</number>
                  </property>
                  <item>
                   <layout class="QGridLayout" name="gridLayout_55">
                    <item row="2" column="0" colspan="3">
                     <widget class="QLineEdit" name="lineEdit_26">
                      <property name="minimumSize">
                       <size>
                        <width>75</width>
                        <height>21</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>9999</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="text">
                       <string>0.0</string>
                      </property>
                      <property name="maxLength">
                       <number>4</number>
                      </property>
                     </widget>
                    </item>
                    <item row="0" column="0" colspan="3">
                     <widget class="QCheckBox" name="checkBox_26">
                      <property name="minimumSize">
                       <size>
                        <width>50</width>
                        <height>20</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>9999</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <family>Microsoft YaHei</family>
                        <pointsize>12</pointsize>
                       </font>
                      </property>
                      <property name="styleSheet">
                       <string notr="true">background-color: rgb(50, 57, 255);
color: rgb(250, 255, 255);border:1px solid white;</string>
                      </property>
                      <property name="text">
                       <string>26</string>
                      </property>
                      <property name="checked">
                       <bool>true</bool>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="0">
                     <spacer name="horizontalSpacer_109">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                    <item row="1" column="1">
                     <widget class="QSlider" name="verticalSlider_26">
                      <property name="minimumSize">
                       <size>
                        <width>16</width>
                        <height>151</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>16</width>
                        <height>151</height>
                       </size>
                      </property>
                      <property name="maximum">
                       <number>1000</number>
                      </property>
                      <property name="orientation">
                       <enum>Qt::Vertical</enum>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="2">
                     <spacer name="horizontalSpacer_110">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                   </layout>
                  </item>
                 </layout>
                </widget>
               </item>
               <item>
                <widget class="QWidget" name="widget_27" native="true">
                 <layout class="QVBoxLayout" name="verticalLayout_30">
                  <property name="spacing">
                   <number>0</number>
                  </property>
                  <property name="leftMargin">
                   <number>0</number>
                  </property>
                  <property name="topMargin">
                   <number>0</number>
                  </property>
                  <property name="rightMargin">
                   <number>0</number>
                  </property>
                  <property name="bottomMargin">
                   <number>0</number>
                  </property>
                  <item>
                   <layout class="QGridLayout" name="gridLayout_56">
                    <item row="2" column="0" colspan="3">
                     <widget class="QLineEdit" name="lineEdit_27">
                      <property name="minimumSize">
                       <size>
                        <width>75</width>
                        <height>21</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>9999</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="text">
                       <string>0.0</string>
                      </property>
                      <property name="maxLength">
                       <number>4</number>
                      </property>
                     </widget>
                    </item>
                    <item row="0" column="0" colspan="3">
                     <widget class="QCheckBox" name="checkBox_27">
                      <property name="minimumSize">
                       <size>
                        <width>50</width>
                        <height>20</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>9999</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <family>Microsoft YaHei</family>
                        <pointsize>12</pointsize>
                       </font>
                      </property>
                      <property name="styleSheet">
                       <string notr="true">background-color: rgb(50, 57, 255);
color: rgb(250, 255, 255);border:1px solid white;</string>
                      </property>
                      <property name="text">
                       <string>27</string>
                      </property>
                      <property name="checked">
                       <bool>true</bool>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="0">
                     <spacer name="horizontalSpacer_111">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                    <item row="1" column="1">
                     <widget class="QSlider" name="verticalSlider_27">
                      <property name="minimumSize">
                       <size>
                        <width>16</width>
                        <height>151</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>16</width>
                        <height>151</height>
                       </size>
                      </property>
                      <property name="maximum">
                       <number>1000</number>
                      </property>
                      <property name="orientation">
                       <enum>Qt::Vertical</enum>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="2">
                     <spacer name="horizontalSpacer_112">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                   </layout>
                  </item>
                 </layout>
                </widget>
               </item>
               <item>
                <widget class="QWidget" name="widget_28" native="true">
                 <layout class="QVBoxLayout" name="verticalLayout_31">
                  <property name="spacing">
                   <number>0</number>
                  </property>
                  <property name="leftMargin">
                   <number>0</number>
                  </property>
                  <property name="topMargin">
                   <number>0</number>
                  </property>
                  <property name="rightMargin">
                   <number>0</number>
                  </property>
                  <property name="bottomMargin">
                   <number>0</number>
                  </property>
                  <item>
                   <layout class="QGridLayout" name="gridLayout_57">
                    <item row="2" column="0" colspan="3">
                     <widget class="QLineEdit" name="lineEdit_28">
                      <property name="minimumSize">
                       <size>
                        <width>75</width>
                        <height>21</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>9999</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="text">
                       <string>0.0</string>
                      </property>
                      <property name="maxLength">
                       <number>4</number>
                      </property>
                     </widget>
                    </item>
                    <item row="0" column="0" colspan="3">
                     <widget class="QCheckBox" name="checkBox_28">
                      <property name="minimumSize">
                       <size>
                        <width>50</width>
                        <height>20</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>9999</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <family>Microsoft YaHei</family>
                        <pointsize>12</pointsize>
                       </font>
                      </property>
                      <property name="styleSheet">
                       <string notr="true">background-color: rgb(50, 57, 255);
color: rgb(250, 255, 255);border:1px solid white;</string>
                      </property>
                      <property name="text">
                       <string>28</string>
                      </property>
                      <property name="checked">
                       <bool>true</bool>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="0">
                     <spacer name="horizontalSpacer_113">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                    <item row="1" column="1">
                     <widget class="QSlider" name="verticalSlider_28">
                      <property name="minimumSize">
                       <size>
                        <width>16</width>
                        <height>151</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>16</width>
                        <height>151</height>
                       </size>
                      </property>
                      <property name="maximum">
                       <number>1000</number>
                      </property>
                      <property name="orientation">
                       <enum>Qt::Vertical</enum>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="2">
                     <spacer name="horizontalSpacer_114">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                   </layout>
                  </item>
                 </layout>
                </widget>
               </item>
               <item>
                <widget class="QWidget" name="widget_29" native="true">
                 <layout class="QVBoxLayout" name="verticalLayout_32">
                  <property name="spacing">
                   <number>0</number>
                  </property>
                  <property name="leftMargin">
                   <number>0</number>
                  </property>
                  <property name="topMargin">
                   <number>0</number>
                  </property>
                  <property name="rightMargin">
                   <number>0</number>
                  </property>
                  <property name="bottomMargin">
                   <number>0</number>
                  </property>
                  <item>
                   <layout class="QGridLayout" name="gridLayout_58">
                    <item row="2" column="0" colspan="3">
                     <widget class="QLineEdit" name="lineEdit_29">
                      <property name="minimumSize">
                       <size>
                        <width>75</width>
                        <height>21</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>9999</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="text">
                       <string>0.0</string>
                      </property>
                      <property name="maxLength">
                       <number>4</number>
                      </property>
                     </widget>
                    </item>
                    <item row="0" column="0" colspan="3">
                     <widget class="QCheckBox" name="checkBox_29">
                      <property name="minimumSize">
                       <size>
                        <width>50</width>
                        <height>20</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>9999</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <family>Microsoft YaHei</family>
                        <pointsize>12</pointsize>
                       </font>
                      </property>
                      <property name="styleSheet">
                       <string notr="true">background-color: rgb(50, 57, 255);
color: rgb(250, 255, 255);border:1px solid white;</string>
                      </property>
                      <property name="text">
                       <string>29</string>
                      </property>
                      <property name="checked">
                       <bool>true</bool>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="0">
                     <spacer name="horizontalSpacer_115">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                    <item row="1" column="1">
                     <widget class="QSlider" name="verticalSlider_29">
                      <property name="minimumSize">
                       <size>
                        <width>16</width>
                        <height>151</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>16</width>
                        <height>151</height>
                       </size>
                      </property>
                      <property name="maximum">
                       <number>1000</number>
                      </property>
                      <property name="orientation">
                       <enum>Qt::Vertical</enum>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="2">
                     <spacer name="horizontalSpacer_116">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                   </layout>
                  </item>
                 </layout>
                </widget>
               </item>
               <item>
                <widget class="QWidget" name="widget_30" native="true">
                 <layout class="QVBoxLayout" name="verticalLayout_33">
                  <property name="spacing">
                   <number>0</number>
                  </property>
                  <property name="leftMargin">
                   <number>0</number>
                  </property>
                  <property name="topMargin">
                   <number>0</number>
                  </property>
                  <property name="rightMargin">
                   <number>0</number>
                  </property>
                  <property name="bottomMargin">
                   <number>0</number>
                  </property>
                  <item>
                   <layout class="QGridLayout" name="gridLayout_59">
                    <item row="2" column="0" colspan="3">
                     <widget class="QLineEdit" name="lineEdit_30">
                      <property name="minimumSize">
                       <size>
                        <width>75</width>
                        <height>21</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>9999</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="text">
                       <string>0.0</string>
                      </property>
                      <property name="maxLength">
                       <number>4</number>
                      </property>
                     </widget>
                    </item>
                    <item row="0" column="0" colspan="3">
                     <widget class="QCheckBox" name="checkBox_30">
                      <property name="minimumSize">
                       <size>
                        <width>50</width>
                        <height>20</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>9999</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <family>Microsoft YaHei</family>
                        <pointsize>12</pointsize>
                       </font>
                      </property>
                      <property name="styleSheet">
                       <string notr="true">background-color: rgb(50, 57, 255);
color: rgb(250, 255, 255);border:1px solid white;</string>
                      </property>
                      <property name="text">
                       <string>30</string>
                      </property>
                      <property name="checked">
                       <bool>true</bool>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="0">
                     <spacer name="horizontalSpacer_117">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                    <item row="1" column="1">
                     <widget class="QSlider" name="verticalSlider_30">
                      <property name="minimumSize">
                       <size>
                        <width>16</width>
                        <height>151</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>16</width>
                        <height>151</height>
                       </size>
                      </property>
                      <property name="maximum">
                       <number>1000</number>
                      </property>
                      <property name="orientation">
                       <enum>Qt::Vertical</enum>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="2">
                     <spacer name="horizontalSpacer_118">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                   </layout>
                  </item>
                 </layout>
                </widget>
               </item>
               <item>
                <widget class="QWidget" name="widget_31" native="true">
                 <layout class="QVBoxLayout" name="verticalLayout_34">
                  <property name="spacing">
                   <number>0</number>
                  </property>
                  <property name="leftMargin">
                   <number>0</number>
                  </property>
                  <property name="topMargin">
                   <number>0</number>
                  </property>
                  <property name="rightMargin">
                   <number>0</number>
                  </property>
                  <property name="bottomMargin">
                   <number>0</number>
                  </property>
                  <item>
                   <layout class="QGridLayout" name="gridLayout_60">
                    <item row="2" column="0" colspan="3">
                     <widget class="QLineEdit" name="lineEdit_31">
                      <property name="minimumSize">
                       <size>
                        <width>75</width>
                        <height>21</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>9999</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="text">
                       <string>0.0</string>
                      </property>
                      <property name="maxLength">
                       <number>4</number>
                      </property>
                     </widget>
                    </item>
                    <item row="0" column="0" colspan="3">
                     <widget class="QCheckBox" name="checkBox_31">
                      <property name="minimumSize">
                       <size>
                        <width>50</width>
                        <height>20</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>9999</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <family>Microsoft YaHei</family>
                        <pointsize>12</pointsize>
                       </font>
                      </property>
                      <property name="styleSheet">
                       <string notr="true">background-color: rgb(50, 57, 255);
color: rgb(250, 255, 255);border:1px solid white;</string>
                      </property>
                      <property name="text">
                       <string>31</string>
                      </property>
                      <property name="checked">
                       <bool>true</bool>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="0">
                     <spacer name="horizontalSpacer_119">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                    <item row="1" column="1">
                     <widget class="QSlider" name="verticalSlider_31">
                      <property name="minimumSize">
                       <size>
                        <width>16</width>
                        <height>151</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>16</width>
                        <height>151</height>
                       </size>
                      </property>
                      <property name="maximum">
                       <number>1000</number>
                      </property>
                      <property name="orientation">
                       <enum>Qt::Vertical</enum>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="2">
                     <spacer name="horizontalSpacer_120">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                   </layout>
                  </item>
                 </layout>
                </widget>
               </item>
               <item>
                <widget class="QWidget" name="widget_32" native="true">
                 <layout class="QVBoxLayout" name="verticalLayout_35">
                  <property name="spacing">
                   <number>0</number>
                  </property>
                  <property name="leftMargin">
                   <number>0</number>
                  </property>
                  <property name="topMargin">
                   <number>0</number>
                  </property>
                  <property name="rightMargin">
                   <number>0</number>
                  </property>
                  <property name="bottomMargin">
                   <number>0</number>
                  </property>
                  <item>
                   <layout class="QGridLayout" name="gridLayout_61">
                    <item row="2" column="0" colspan="3">
                     <widget class="QLineEdit" name="lineEdit_32">
                      <property name="minimumSize">
                       <size>
                        <width>75</width>
                        <height>21</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>9999</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="text">
                       <string>0.0</string>
                      </property>
                      <property name="maxLength">
                       <number>4</number>
                      </property>
                     </widget>
                    </item>
                    <item row="0" column="0" colspan="3">
                     <widget class="QCheckBox" name="checkBox_32">
                      <property name="minimumSize">
                       <size>
                        <width>50</width>
                        <height>20</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>9999</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <family>Microsoft YaHei</family>
                        <pointsize>12</pointsize>
                       </font>
                      </property>
                      <property name="styleSheet">
                       <string notr="true">background-color: rgb(50, 57, 255);
color: rgb(250, 255, 255);border:1px solid white;</string>
                      </property>
                      <property name="text">
                       <string>32</string>
                      </property>
                      <property name="checked">
                       <bool>true</bool>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="0">
                     <spacer name="horizontalSpacer_121">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                    <item row="1" column="1">
                     <widget class="QSlider" name="verticalSlider_32">
                      <property name="minimumSize">
                       <size>
                        <width>16</width>
                        <height>151</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>16</width>
                        <height>151</height>
                       </size>
                      </property>
                      <property name="maximum">
                       <number>1000</number>
                      </property>
                      <property name="orientation">
                       <enum>Qt::Vertical</enum>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="2">
                     <spacer name="horizontalSpacer_122">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                   </layout>
                  </item>
                 </layout>
                </widget>
               </item>
               <item>
                <widget class="QWidget" name="widget_33" native="true">
                 <layout class="QVBoxLayout" name="verticalLayout_36">
                  <property name="spacing">
                   <number>0</number>
                  </property>
                  <property name="leftMargin">
                   <number>0</number>
                  </property>
                  <property name="topMargin">
                   <number>0</number>
                  </property>
                  <property name="rightMargin">
                   <number>0</number>
                  </property>
                  <property name="bottomMargin">
                   <number>0</number>
                  </property>
                  <item>
                   <layout class="QGridLayout" name="gridLayout_62">
                    <item row="2" column="0" colspan="3">
                     <widget class="QLineEdit" name="lineEdit_33">
                      <property name="minimumSize">
                       <size>
                        <width>75</width>
                        <height>21</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>9999</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="text">
                       <string>0.0</string>
                      </property>
                      <property name="maxLength">
                       <number>4</number>
                      </property>
                     </widget>
                    </item>
                    <item row="0" column="0" colspan="3">
                     <widget class="QCheckBox" name="checkBox_33">
                      <property name="minimumSize">
                       <size>
                        <width>50</width>
                        <height>20</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>9999</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <family>Microsoft YaHei</family>
                        <pointsize>12</pointsize>
                       </font>
                      </property>
                      <property name="styleSheet">
                       <string notr="true">background-color: rgb(50, 57, 255);
color: rgb(250, 255, 255);border:1px solid white;</string>
                      </property>
                      <property name="text">
                       <string>33</string>
                      </property>
                      <property name="checked">
                       <bool>true</bool>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="0">
                     <spacer name="horizontalSpacer_123">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                    <item row="1" column="1">
                     <widget class="QSlider" name="verticalSlider_33">
                      <property name="minimumSize">
                       <size>
                        <width>16</width>
                        <height>151</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>16</width>
                        <height>151</height>
                       </size>
                      </property>
                      <property name="maximum">
                       <number>1000</number>
                      </property>
                      <property name="orientation">
                       <enum>Qt::Vertical</enum>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="2">
                     <spacer name="horizontalSpacer_124">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                   </layout>
                  </item>
                 </layout>
                </widget>
               </item>
               <item>
                <widget class="QWidget" name="widget_34" native="true">
                 <layout class="QVBoxLayout" name="verticalLayout_37">
                  <property name="spacing">
                   <number>0</number>
                  </property>
                  <property name="leftMargin">
                   <number>0</number>
                  </property>
                  <property name="topMargin">
                   <number>0</number>
                  </property>
                  <property name="rightMargin">
                   <number>0</number>
                  </property>
                  <property name="bottomMargin">
                   <number>0</number>
                  </property>
                  <item>
                   <layout class="QGridLayout" name="gridLayout_63">
                    <item row="2" column="0" colspan="3">
                     <widget class="QLineEdit" name="lineEdit_34">
                      <property name="minimumSize">
                       <size>
                        <width>75</width>
                        <height>21</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>9999</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="text">
                       <string>0.0</string>
                      </property>
                      <property name="maxLength">
                       <number>4</number>
                      </property>
                     </widget>
                    </item>
                    <item row="0" column="0" colspan="3">
                     <widget class="QCheckBox" name="checkBox_34">
                      <property name="minimumSize">
                       <size>
                        <width>50</width>
                        <height>20</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>9999</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <family>Microsoft YaHei</family>
                        <pointsize>12</pointsize>
                       </font>
                      </property>
                      <property name="styleSheet">
                       <string notr="true">background-color: rgb(50, 57, 255);
color: rgb(250, 255, 255);border:1px solid white;</string>
                      </property>
                      <property name="text">
                       <string>34</string>
                      </property>
                      <property name="checked">
                       <bool>true</bool>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="0">
                     <spacer name="horizontalSpacer_125">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                    <item row="1" column="1">
                     <widget class="QSlider" name="verticalSlider_34">
                      <property name="minimumSize">
                       <size>
                        <width>16</width>
                        <height>151</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>16</width>
                        <height>151</height>
                       </size>
                      </property>
                      <property name="maximum">
                       <number>1000</number>
                      </property>
                      <property name="orientation">
                       <enum>Qt::Vertical</enum>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="2">
                     <spacer name="horizontalSpacer_126">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                   </layout>
                  </item>
                 </layout>
                </widget>
               </item>
               <item>
                <widget class="QWidget" name="widget_35" native="true">
                 <layout class="QVBoxLayout" name="verticalLayout_38">
                  <property name="spacing">
                   <number>0</number>
                  </property>
                  <property name="leftMargin">
                   <number>0</number>
                  </property>
                  <property name="topMargin">
                   <number>0</number>
                  </property>
                  <property name="rightMargin">
                   <number>0</number>
                  </property>
                  <property name="bottomMargin">
                   <number>0</number>
                  </property>
                  <item>
                   <layout class="QGridLayout" name="gridLayout_64">
                    <item row="2" column="0" colspan="3">
                     <widget class="QLineEdit" name="lineEdit_35">
                      <property name="minimumSize">
                       <size>
                        <width>75</width>
                        <height>21</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>9999</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="text">
                       <string>0.0</string>
                      </property>
                      <property name="maxLength">
                       <number>4</number>
                      </property>
                     </widget>
                    </item>
                    <item row="0" column="0" colspan="3">
                     <widget class="QCheckBox" name="checkBox_35">
                      <property name="minimumSize">
                       <size>
                        <width>50</width>
                        <height>20</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>9999</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <family>Microsoft YaHei</family>
                        <pointsize>12</pointsize>
                       </font>
                      </property>
                      <property name="styleSheet">
                       <string notr="true">background-color: rgb(50, 57, 255);
color: rgb(250, 255, 255);border:1px solid white;</string>
                      </property>
                      <property name="text">
                       <string>35</string>
                      </property>
                      <property name="checked">
                       <bool>true</bool>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="0">
                     <spacer name="horizontalSpacer_127">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                    <item row="1" column="1">
                     <widget class="QSlider" name="verticalSlider_35">
                      <property name="minimumSize">
                       <size>
                        <width>16</width>
                        <height>151</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>16</width>
                        <height>151</height>
                       </size>
                      </property>
                      <property name="maximum">
                       <number>1000</number>
                      </property>
                      <property name="orientation">
                       <enum>Qt::Vertical</enum>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="2">
                     <spacer name="horizontalSpacer_128">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                   </layout>
                  </item>
                 </layout>
                </widget>
               </item>
               <item>
                <widget class="QWidget" name="widget_36" native="true">
                 <layout class="QVBoxLayout" name="verticalLayout_39">
                  <property name="spacing">
                   <number>0</number>
                  </property>
                  <property name="leftMargin">
                   <number>0</number>
                  </property>
                  <property name="topMargin">
                   <number>0</number>
                  </property>
                  <property name="rightMargin">
                   <number>0</number>
                  </property>
                  <property name="bottomMargin">
                   <number>0</number>
                  </property>
                  <item>
                   <layout class="QGridLayout" name="gridLayout_65">
                    <item row="2" column="0" colspan="3">
                     <widget class="QLineEdit" name="lineEdit_36">
                      <property name="minimumSize">
                       <size>
                        <width>75</width>
                        <height>21</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>9999</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="text">
                       <string>0.0</string>
                      </property>
                      <property name="maxLength">
                       <number>4</number>
                      </property>
                     </widget>
                    </item>
                    <item row="0" column="0" colspan="3">
                     <widget class="QCheckBox" name="checkBox_36">
                      <property name="minimumSize">
                       <size>
                        <width>50</width>
                        <height>20</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>9999</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <family>Microsoft YaHei</family>
                        <pointsize>12</pointsize>
                       </font>
                      </property>
                      <property name="styleSheet">
                       <string notr="true">background-color: rgb(50, 57, 255);
color: rgb(250, 255, 255);border:1px solid white;</string>
                      </property>
                      <property name="text">
                       <string>36</string>
                      </property>
                      <property name="checked">
                       <bool>true</bool>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="0">
                     <spacer name="horizontalSpacer_129">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                    <item row="1" column="1">
                     <widget class="QSlider" name="verticalSlider_36">
                      <property name="minimumSize">
                       <size>
                        <width>16</width>
                        <height>151</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>16</width>
                        <height>151</height>
                       </size>
                      </property>
                      <property name="maximum">
                       <number>1000</number>
                      </property>
                      <property name="orientation">
                       <enum>Qt::Vertical</enum>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="2">
                     <spacer name="horizontalSpacer_130">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                   </layout>
                  </item>
                 </layout>
                </widget>
               </item>
               <item>
                <widget class="QWidget" name="widget_37" native="true">
                 <layout class="QVBoxLayout" name="verticalLayout_40">
                  <property name="spacing">
                   <number>0</number>
                  </property>
                  <property name="leftMargin">
                   <number>0</number>
                  </property>
                  <property name="topMargin">
                   <number>0</number>
                  </property>
                  <property name="rightMargin">
                   <number>0</number>
                  </property>
                  <property name="bottomMargin">
                   <number>0</number>
                  </property>
                  <item>
                   <layout class="QGridLayout" name="gridLayout_66">
                    <item row="2" column="0" colspan="3">
                     <widget class="QLineEdit" name="lineEdit_37">
                      <property name="minimumSize">
                       <size>
                        <width>75</width>
                        <height>21</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>9999</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="text">
                       <string>0.0</string>
                      </property>
                      <property name="maxLength">
                       <number>4</number>
                      </property>
                     </widget>
                    </item>
                    <item row="0" column="0" colspan="3">
                     <widget class="QCheckBox" name="checkBox_37">
                      <property name="minimumSize">
                       <size>
                        <width>50</width>
                        <height>20</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>9999</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <family>Microsoft YaHei</family>
                        <pointsize>12</pointsize>
                       </font>
                      </property>
                      <property name="styleSheet">
                       <string notr="true">background-color: rgb(50, 57, 255);
color: rgb(250, 255, 255);border:1px solid white;</string>
                      </property>
                      <property name="text">
                       <string>37</string>
                      </property>
                      <property name="checked">
                       <bool>true</bool>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="0">
                     <spacer name="horizontalSpacer_131">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                    <item row="1" column="1">
                     <widget class="QSlider" name="verticalSlider_37">
                      <property name="minimumSize">
                       <size>
                        <width>16</width>
                        <height>151</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>16</width>
                        <height>151</height>
                       </size>
                      </property>
                      <property name="maximum">
                       <number>1000</number>
                      </property>
                      <property name="orientation">
                       <enum>Qt::Vertical</enum>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="2">
                     <spacer name="horizontalSpacer_132">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                   </layout>
                  </item>
                 </layout>
                </widget>
               </item>
              </layout>
             </widget>
            </item>
           </layout>
          </widget>
         </widget>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="tab_39">
       <attribute name="title">
        <string>太阳灯</string>
       </attribute>
       <layout class="QGridLayout" name="gridLayout_13">
        <item row="0" column="0">
         <widget class="QGroupBox" name="groupBox_41">
          <property name="maximumSize">
           <size>
            <width>500</width>
            <height>100</height>
           </size>
          </property>
          <property name="title">
           <string>通讯设置</string>
          </property>
          <widget class="QWidget" name="layoutWidget">
           <property name="geometry">
            <rect>
             <x>14</x>
             <y>40</y>
             <width>372</width>
             <height>42</height>
            </rect>
           </property>
           <layout class="QHBoxLayout" name="horizontalLayout_22">
            <item>
             <widget class="QLabel" name="label_163">
              <property name="font">
               <font>
                <family>Microsoft YaHei</family>
                <pointsize>12</pointsize>
               </font>
              </property>
              <property name="text">
               <string>光源：</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QComboBox" name="comboBox_serials_Le007">
              <property name="minimumSize">
               <size>
                <width>140</width>
                <height>40</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>80</width>
                <height>16777215</height>
               </size>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="pushButton_le007_connect">
              <property name="text">
               <string>连接</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="pushButton_le007_status">
              <property name="maximumSize">
               <size>
                <width>20</width>
                <height>40</height>
               </size>
              </property>
              <property name="text">
               <string/>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
         </widget>
        </item>
        <item row="1" column="0" colspan="2">
         <widget class="QGroupBox" name="groupBox_9">
          <property name="title">
           <string/>
          </property>
          <layout class="QGridLayout" name="gridLayout_14">
           <item row="1" column="0">
            <layout class="QHBoxLayout" name="horizontalLayout_16">
             <item>
              <widget class="QGroupBox" name="groupBox_16">
               <property name="title">
                <string>5</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QGroupBox" name="groupBox_18">
               <property name="title">
                <string>6</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QGroupBox" name="groupBox_19">
               <property name="title">
                <string>7</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QGroupBox" name="groupBox_20">
               <property name="title">
                <string>8</string>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item row="2" column="0">
            <layout class="QHBoxLayout" name="horizontalLayout_17">
             <item>
              <widget class="QGroupBox" name="groupBox_21">
               <property name="title">
                <string>9</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QGroupBox" name="groupBox_23">
               <property name="title">
                <string>10</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QGroupBox" name="groupBox_24">
               <property name="title">
                <string>11</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QGroupBox" name="groupBox_25">
               <property name="title">
                <string>12</string>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item row="0" column="0">
            <layout class="QHBoxLayout" name="horizontalLayout_15">
             <item>
              <widget class="QGroupBox" name="groupBox_10">
               <property name="minimumSize">
                <size>
                 <width>200</width>
                 <height>250</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>300</height>
                </size>
               </property>
               <property name="title">
                <string>1</string>
               </property>
               <widget class="QPushButton" name="pushButton_le007_left_send">
                <property name="enabled">
                 <bool>false</bool>
                </property>
                <property name="geometry">
                 <rect>
                  <x>50</x>
                  <y>160</y>
                  <width>151</width>
                  <height>51</height>
                 </rect>
                </property>
                <property name="text">
                 <string>SEND</string>
                </property>
                <property name="icon">
                 <iconset>
                  <normaloff>C:/Users/<USER>/.designer/backup/Source/icons/开始.png</normaloff>C:/Users/<USER>/.designer/backup/Source/icons/开始.png</iconset>
                </property>
                <property name="iconSize">
                 <size>
                  <width>40</width>
                  <height>40</height>
                 </size>
                </property>
               </widget>
               <widget class="QWidget" name="layoutWidget">
                <property name="geometry">
                 <rect>
                  <x>20</x>
                  <y>110</y>
                  <width>353</width>
                  <height>42</height>
                 </rect>
                </property>
                <layout class="QHBoxLayout" name="horizontalLayout_18">
                 <item>
                  <widget class="QLabel" name="label_9">
                   <property name="maximumSize">
                    <size>
                     <width>30</width>
                     <height>16777215</height>
                    </size>
                   </property>
                   <property name="text">
                    <string>L:</string>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <widget class="QSpinBox" name="spinBox_le007_left_illum">
                   <property name="minimumSize">
                    <size>
                     <width>0</width>
                     <height>40</height>
                    </size>
                   </property>
                   <property name="minimum">
                    <number>1</number>
                   </property>
                   <property name="maximum">
                    <number>155</number>
                   </property>
                   <property name="singleStep">
                    <number>100</number>
                   </property>
                   <property name="value">
                    <number>155</number>
                   </property>
                  </widget>
                 </item>
                </layout>
               </widget>
              </widget>
             </item>
             <item>
              <widget class="QGroupBox" name="groupBox_13">
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>16777215</height>
                </size>
               </property>
               <property name="title">
                <string>2</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QGroupBox" name="groupBox_14">
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>16777215</height>
                </size>
               </property>
               <property name="title">
                <string>3</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QGroupBox" name="groupBox_15">
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>16777215</height>
                </size>
               </property>
               <property name="title">
                <string>4</string>
               </property>
              </widget>
             </item>
            </layout>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </widget>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>1694</width>
     <height>26</height>
    </rect>
   </property>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
  <action name="action_led">
   <property name="text">
    <string>led</string>
   </property>
  </action>
  <action name="actionled_linear">
   <property name="text">
    <string>led_linear</string>
   </property>
  </action>
 </widget>
 <resources>
  <include location="CMS.qrc"/>
  <include location="CMS.qrc"/>
 </resources>
 <connections/>
 <buttongroups>
  <buttongroup name="buttonGroup_choose_spd"/>
 </buttongroups>
</ui>
