from scipy.optimize import minimize
import numpy as np
import time
from scipy.interpolate import interp1d
from scipy.optimize import  minimize
import os
import pandas as pd
import matplotlib.pyplot as plt

# FIXME:ndarray形式：43x18
global CIE_RGB
print(time.strftime('%Y-%m-%d %H:%M:%S'))
CIE_RGB=np.array(
    [[1.299000e-04, 3.917000e-06, 6.061000e-04],
       [4.149000e-04, 1.239000e-05, 1.946000e-03],
       [1.368000e-03, 3.900000e-05, 6.450001e-03],
       [4.243000e-03, 1.200000e-04, 2.005001e-02],
       [1.431000e-02, 3.960000e-04, 6.785001e-02],
       [4.351000e-02, 1.210000e-03, 2.074000e-01],
       [1.343800e-01, 4.000000e-03, 6.456000e-01],
       [2.839000e-01, 1.160000e-02, 1.385600e+00],
       [3.482800e-01, 2.300000e-02, 1.747060e+00],
       [3.362000e-01, 3.800000e-02, 1.772110e+00],
       [2.908000e-01, 6.000000e-02, 1.669200e+00],
       [1.953600e-01, 9.098000e-02, 1.287640e+00],
       [9.564000e-02, 1.390200e-01, 8.129501e-01],
       [3.201000e-02, 2.080200e-01, 4.651800e-01],
       [4.900000e-03, 3.230000e-01, 2.720000e-01],
       [9.300000e-03, 5.030000e-01, 1.582000e-01],
       [6.327000e-02, 7.100000e-01, 7.824999e-02],
       [1.655000e-01, 8.620000e-01, 4.216000e-02],
       [2.904000e-01, 9.540000e-01, 2.030000e-02],
       [4.334499e-01, 9.949501e-01, 8.749999e-03],
       [5.945000e-01, 9.950000e-01, 3.900000e-03],
       [7.621000e-01, 9.520000e-01, 2.100000e-03],
       [9.163000e-01, 8.700000e-01, 1.650001e-03],
       [1.026300e+00, 7.570000e-01, 1.100000e-03],
       [1.062200e+00, 6.310000e-01, 8.000000e-04],
       [1.002600e+00, 5.030000e-01, 3.400000e-04],
       [8.544499e-01, 3.810000e-01, 1.900000e-04],
       [6.424000e-01, 2.650000e-01, 5.000000e-05],
       [4.479000e-01, 1.750000e-01, 2.000000e-05],
       [2.835000e-01, 1.070000e-01, 0.000000e+00],
       [1.649000e-01, 6.100000e-02, 0.000000e+00],
       [8.740000e-02, 3.200000e-02, 0.000000e+00],
       [4.677000e-02, 1.700000e-02, 0.000000e+00],
       [2.270000e-02, 8.210000e-03, 0.000000e+00],
       [1.135916e-02, 4.102000e-03, 0.000000e+00],
       [5.790346e-03, 2.091000e-03, 0.000000e+00],
       [2.899327e-03, 1.047000e-03, 0.000000e+00],
       [1.439971e-03, 5.200000e-04, 0.000000e+00],
       [6.900790e-04, 2.492000e-04, 0.000000e+00],
       [3.323010e-04, 1.200000e-04, 0.000000e+00],
       [1.661510e-04, 6.000000e-05, 0.000000e+00],
       [8.307530e-05, 3.000000e-05, 0.000000e+00],
       [4.150990e-05, 1.499000e-05, 0.000000e+00]]
)

visionFunc = {380: 4e-05, 381: 4.4000000000000006e-05, 382: 4.8e-05, 383: 5.2000000000000004e-05,
              384: 5.6000000000000006e-05, 385: 6e-05, 386: 7.2e-05, 387: 8.400000000000001e-05,
              388: 9.6e-05, 389: 0.000108, 390: 0.00012, 391: 0.0001394, 392: 0.0001588,
              393: 0.0001782, 394: 0.00019759999999999998, 395: 0.000217, 396: 0.0002536,
              397: 0.0002902, 398: 0.00032680000000000003, 399: 0.00036340000000000005,
              400: 0.0004000000000000001, 401: 0.00044800000000000005, 402: 0.000496,
              403: 0.000544, 404: 0.0005920000000000001, 405: 0.00064, 406: 0.000754,
              407: 0.0008680000000000001, 408: 0.000982, 409: 0.001096, 410: 0.00121,
              411: 0.0014039999999999999, 412: 0.001598, 413: 0.001792, 414: 0.001986,
              415: 0.00218, 416: 0.0025440000000000003, 417: 0.002908, 418: 0.003272,
              419: 0.0036360000000000003, 420: 0.004, 421: 0.00466, 422: 0.00532,
              423: 0.00598, 424: 0.00664, 425: 0.0073, 426: 0.00816, 427: 0.00902,
              428: 0.00988, 429: 0.01074, 430: 0.0116, 431: 0.012639999999999998,
              432: 0.01368, 433: 0.014719999999999999, 434: 0.01576, 435: 0.0168,
              436: 0.01804, 437: 0.01928, 438: 0.02052, 439: 0.02176, 440: 0.023,
              441: 0.02436, 442: 0.02572, 443: 0.02708, 444: 0.02844, 445: 0.0298,
              446: 0.03144, 447: 0.03308, 448: 0.03472, 449: 0.036359999999999996,
              450: 0.038, 451: 0.04, 452: 0.042, 453: 0.044, 454: 0.046, 455: 0.048,
              456: 0.0504, 457: 0.0528, 458: 0.0552, 459: 0.0576, 460: 0.06, 461: 0.06278,
              462: 0.06556, 463: 0.06834, 464: 0.07111999999999999, 465: 0.0739, 466: 0.07732,
              467: 0.08073999999999999, 468: 0.08416, 469: 0.08757999999999999, 470: 0.091,
              471: 0.0954, 472: 0.0998, 473: 0.1042, 474: 0.1086, 475: 0.113, 476: 0.1182,
              477: 0.12340000000000001, 478: 0.12860000000000002, 479: 0.1338, 480: 0.139,
              481: 0.14500000000000002, 482: 0.15100000000000002, 483: 0.15700000000000003,
              484: 0.163, 485: 0.169, 486: 0.1768, 487: 0.18460000000000001, 488: 0.19240000000000002,
              489: 0.2002, 490: 0.208, 491: 0.2182, 492: 0.2284, 493: 0.2386, 494: 0.24880000000000002,
              495: 0.259, 496: 0.2718, 497: 0.2846, 498: 0.2974, 499: 0.31020000000000003,
              500: 0.323, 501: 0.3398, 502: 0.3566, 503: 0.37339999999999995, 504: 0.3902,
              505: 0.407, 506: 0.42619999999999997, 507: 0.44539999999999996, 508: 0.4646,
              509: 0.4838, 510: 0.503, 511: 0.524, 512: 0.545, 513: 0.5660000000000001,
              514: 0.587, 515: 0.608, 516: 0.6284, 517: 0.6487999999999999, 518: 0.6692,
              519: 0.6896, 520: 0.71, 521: 0.7266, 522: 0.7432, 523: 0.7598, 524: 0.7764, 525: 0.793,
              526: 0.8068000000000001, 527: 0.8206, 528: 0.8344, 529: 0.8482, 530: 0.862, 531: 0.8726,
              532: 0.8832, 533: 0.8938, 534: 0.9044, 535: 0.915, 536: 0.9228000000000001, 537: 0.9306,
              538: 0.9384, 539: 0.9461999999999999, 540: 0.954, 541: 0.9591999999999999, 542: 0.9643999999999999,
              543: 0.9696, 544: 0.9748, 545: 0.98, 546: 0.983, 547: 0.986, 548: 0.989, 549: 0.992, 550: 0.995,
              551: 0.996, 552: 0.997, 553: 0.998, 554: 0.999, 555: 1.0, 556: 0.999, 557: 0.998, 558: 0.997,
              559: 0.996, 560: 0.995, 561: 0.9918, 562: 0.9886, 563: 0.9853999999999999, 564: 0.9822,
              565: 0.979, 566: 0.9736, 567: 0.9682, 568: 0.9628, 569: 0.9573999999999999, 570: 0.952,
              571: 0.9446, 572: 0.9372, 573: 0.9298, 574: 0.9224, 575: 0.915, 576: 0.906, 577: 0.897,
              578: 0.888, 579: 0.879, 580: 0.87, 581: 0.8586, 582: 0.8472, 583: 0.8358, 584: 0.8243999999999999,
              585: 0.813, 586: 0.8018, 587: 0.7906, 588: 0.7794, 589: 0.7682, 590: 0.757, 591: 0.7446, 592: 0.7322,
              593: 0.7198, 594: 0.7073999999999999, 595: 0.695, 596: 0.6821999999999999, 597: 0.6694, 598: 0.6566,
              599: 0.6438, 600: 0.631, 601: 0.6182, 602: 0.6053999999999999, 603: 0.5926, 604: 0.5798, 605: 0.567,
              606: 0.5541999999999999, 607: 0.5414, 608: 0.5286, 609: 0.5158, 610: 0.503, 611: 0.4906, 612: 0.4782,
              613: 0.4658, 614: 0.4534, 615: 0.441, 616: 0.429, 617: 0.417, 618: 0.405, 619: 0.393, 620: 0.381,
              621: 0.369, 622: 0.357, 623: 0.345, 624: 0.333, 625: 0.321, 626: 0.3098, 627: 0.29860000000000003,
              628: 0.2874, 629: 0.2762, 630: 0.265, 631: 0.2554, 632: 0.24580000000000002, 633: 0.23620000000000002,
              634: 0.2266, 635: 0.217, 636: 0.2086, 637: 0.2002, 638: 0.1918, 639: 0.1834, 640: 0.175, 641: 0.1676,
              642: 0.1602, 643: 0.1528, 644: 0.1454, 645: 0.138, 646: 0.1318, 647: 0.12560000000000002, 648: 0.1194,
              649: 0.1132, 650: 0.107, 651: 0.10192, 652: 0.09684, 653: 0.09176000000000001, 654: 0.08668000000000001,
              655: 0.0816, 656: 0.07748000000000001, 657: 0.07336000000000001, 658: 0.06924, 659: 0.06512, 660: 0.061,
              661: 0.05772, 662: 0.05444, 663: 0.05116, 664: 0.04788, 665: 0.0446, 666: 0.04208, 667: 0.03956,
              668: 0.037040000000000003, 669: 0.03452, 670: 0.032, 671: 0.03024, 672: 0.02848, 673: 0.02672,
              674: 0.02496, 675: 0.0232, 676: 0.02196, 677: 0.02072, 678: 0.01948, 679: 0.01824, 680: 0.017,
              681: 0.01598, 682: 0.014960000000000001, 683: 0.013940000000000001, 684: 0.012920000000000001,
              685: 0.0119, 686: 0.011162, 687: 0.010424000000000001, 688: 0.009686, 689: 0.008948000000000001,
              690: 0.00821, 691: 0.007712, 692: 0.007214, 693: 0.006716000000000001, 694: 0.0062180000000000004,
              695: 0.00572, 696: 0.005396000000000001, 697: 0.005072, 698: 0.0047480000000000005,
              699: 0.004424000000000001, 700: 0.0041, 701: 0.003866, 702: 0.0036320000000000002,
              703: 0.003398, 704: 0.003164, 705: 0.00293, 706: 0.0027619999999999997, 707: 0.002594,
              708: 0.0024259999999999998, 709: 0.0022579999999999996, 710: 0.00209, 711: 0.0019679999999999997,
              712: 0.001846, 713: 0.0017239999999999998, 714: 0.001602, 715: 0.00148, 716: 0.001394, 717: 0.001308,
              718: 0.001222, 719: 0.0011359999999999999, 720: 0.00105, 721: 0.000988, 722: 0.000926, 723: 0.000864,
              724: 0.000802, 725: 0.00074, 726: 0.000696, 727: 0.000652, 728: 0.000608, 729: 0.0005639999999999999,
              730: 0.00052, 731: 0.00048819999999999994, 732: 0.0004564, 733: 0.00042459999999999997, 734: 0.0003928,
              735: 0.000361, 736: 0.0003386, 737: 0.0003162, 738: 0.0002938, 739: 0.0002714, 740: 0.000249,
              741: 0.00023359999999999999, 742: 0.0002182, 743: 0.0002028, 744: 0.0001874, 745: 0.000172,
              746: 0.0001616, 747: 0.00015120000000000002, 748: 0.0001408, 749: 0.0001304, 750: 0.00012,
              751: 0.00011296, 752: 0.00010592, 753: 9.888e-05, 754: 9.184e-05, 755: 8.48e-05, 756: 7.984e-05,
              757: 7.488e-05, 758: 6.992000000000001e-05, 759: 6.496e-05, 760: 6e-05, 761: 5.648e-05, 762: 5.296e-05,
              763: 4.944e-05, 764: 4.592e-05, 765: 4.24e-05, 766: 3.992e-05, 767: 3.744e-05,
              768: 3.4960000000000004e-05, 769: 3.248e-05, 770: 3e-05, 771: 2.82e-05, 772: 2.64e-05,
              773: 2.46e-05, 774: 2.28e-05, 775: 2.1e-05, 776: 1.98e-05, 777: 1.8599999999999998e-05,
              778: 1.74e-05, 779: 1.62e-05, 780: 1.5e-05}


def processData(ledData,stdData,accuracy,matchMethod):
    """
    预处理data数据
    ledData  shape=>(-1,channel_num):
        360-780
    stdData:
        360-780
    accuracy:
        精确度
    return:
        insertLEDData,insertSTDData
    """

    # 目标数据进行插值
    spdnum = int(np.floor((780-360)/accuracy))+1
    x = np.linspace(360,780,spdnum)

    LEDFunc = interp1d(np.linspace(360,780,np.size(stdData)),stdData,"linear")
    insertSTDData = LEDFunc(x)

    # 对每一个通道进行线性插值
    dataTemp = []
    for data in ledData.T:
        LEDFunc = interp1d(np.linspace(360,780,int(np.size(data))),data,"linear")
        insertLEDData = LEDFunc(x)
        dataTemp.append(insertLEDData)
    insertLEDData = np.asarray(dataTemp).T

    # fixme:返回实际功率值
    powerLedData = insertLEDData

    # fixme:STD_DATA适配LEDDATA->最终目的是降低STD能量值
    if matchMethod == "scale":
        max0 = np.max(insertLEDData,0)
        MAX = np.max(max0)
        MIN = np.min(max0)
        # 求出led均值能量
        power = (MAX + MIN) / 3
        # power = MIN
        # 归一化STD
        insertSTDData = insertSTDData / np.max(insertSTDData) / 3
        # 将std能量进行限制
        # insertSTDData = insertSTDData * power
        insertLEDData = insertLEDData / np.max(insertLEDData)

    if matchMethod == "power":
        max0 = np.max(insertLEDData,0)
        MAX = np.max(max0)
        MIN = np.min(max0)
        # 求出led均值能量
        power = (MAX + MIN) / 3
        # power = MIN
        # 归一化STD
        insertSTDData = insertSTDData / np.max(insertSTDData)
        # 将std能量进行限制
        insertSTDData = insertSTDData * power

    # cie_rgb插值
    dataTemp = []
    for data in CIE_RGB.T:
        linearFunc = interp1d(np.linspace(360,780,np.size(data)),data,"linear")
        insertLinearData = linearFunc(x)
        dataTemp.append(insertLinearData)
    insertCIERGB = np.asarray(dataTemp).T

    return insertLEDData,insertSTDData,insertCIERGB,powerLedData


def get_LED_XYZ(LED_DATA):
    """
    得到LED刺激值
    LED_DATA:ndarray形式
    """
    #LED光源
    LED_XYZ = np.zeros((3,LED_DATA.shape[1]))
    DATA = LED_DATA/np.max(LED_DATA,0)
    for i in range(1,LED_DATA.shape[1]):
        LED_XYZ[:,i] = np.dot(CIE_RGB.T,DATA[:,i])
    return LED_XYZ


def get_STD_XYZ(STD_DATA):
    """
    得到STD刺激值
    STD_DATA:ndarray形式
    """
    # 算标准光源的三刺激值
    # 归一化
    DATA = STD_DATA/np.max(STD_DATA)
    STD_XYZ = np.dot(CIE_RGB.T,DATA)
    return STD_XYZ


def getCIE_XYZ(spd):
    """
    得到结果数据的刺激值
    spd:
        shape->n,
    return:
        XYS.shape->3x1
    """
    spd = spd/np.max(spd)
    XYZ = np.dot(CIE_RGB.T,spd)

    return XYZ


def getCIE_xy(XYZ):
    """
    得到CIE_xy
    """
    x = XYZ[0,0]/np.sum(XYZ)
    y = XYZ[1,0]/np.sum(XYZ)
    return x,y

def getOneCIE_xy(XYZ):
    """
    得到CIE_xy
    """
    x = XYZ[0]/np.sum(XYZ)
    y = XYZ[1]/np.sum(XYZ)
    return x,y

def getCCT(spd):
    """
    计算色温
    """
    CIE_XYZ =  np.dot(CIE_RGB.T,spd)
    CIE_x = CIE_XYZ[0]/np.sum(CIE_XYZ)
    CIE_y = CIE_XYZ[1]/np.sum(CIE_XYZ)

    n = (CIE_x-0.3320)/(0.1858-CIE_y)
    CCT = 437*n**3 + 3601*n**2+6831*n+5517

    return CCT

def getNewSpd(ledData,a):
    """
    得到拟合的spd
    """
    spd = 0

    for i in range(ledData.shape[1]):
        spd += a[i]*ledData[:,i]

    return spd

def computeCd(spd):
    cd = 0
    for index, power in enumerate(spd, start=360):
        if index >= 380:
            cd += visionFunc[index] * power
    cd *= 683
    return cd

def getCdScale(spd,cdTarget):
    # spd，当前光谱,lenth=420
    # cd,目标亮度
    # 计算当前spd亮度
    cdCurrent = computeCd(spd)
    # cdTarget = computeCd(scale * spd ),求scale
    def differ(scale):
        cd = computeCd(scale * spd)
        diff = abs(cdTarget - cd)

        return diff
    res = minimize(differ,0,method="SLSQP")

    scale = res.x[0]
    scale = min(scale,1)
    return scale


# 损失函数
def compute_func(LED_DATA,STD_DATA_target,channel_num,cctTarget,cdTarget):
    def func(x):
        # todo:增添色温限制条件
        s = 0
        for i in range(channel_num):
            s += x[i]*LED_DATA[:,i]
        CIE_XYZ =  np.dot(CIE_RGB.T,s)
        CIE_x = CIE_XYZ[0]/np.sum(CIE_XYZ)
        CIE_y = CIE_XYZ[1]/np.sum(CIE_XYZ)

        n = (CIE_x-0.3320)/(0.1858-CIE_y)

        CCT = 437*n**3 + 3601*n**2+6831*n+5517
        differCCT = abs(CCT - cctTarget)

        differSpd = np.sum(np.abs((s/np.max(s) - STD_DATA_target/np.max(STD_DATA_target))))
        # print("differCCT:", differCCT)
        # print("differSpd:", differSpd)
        # cd = computeCd(s)
        # differCD = abs(cd - cdTarget)
        CIE_XYZ_LED = CIE_XYZ
        CIE_XYZ_STD = np.dot(CIE_RGB.T,STD_DATA_target)
        x_led = CIE_XYZ_LED[0]/np.sum(CIE_XYZ_LED)
        y_led = CIE_XYZ_LED[1]/np.sum(CIE_XYZ_LED)
        x_std = CIE_XYZ_STD[0]/np.sum(CIE_XYZ_STD)
        y_std = CIE_XYZ_STD[1]/np.sum(CIE_XYZ_STD)
        differxy = np.sqrt((x_led-x_std)**2+(y_led-y_std)**2)

        return differSpd+differxy
    return func

# 约束函数：
def constraint_func(Aeq,beq):
    cons = (
            {'type': 'ineq', 'fun': lambda x: x-0},
            {'type': 'ineq', 'fun': lambda x: 1-x},
            # {'type': 'eq', 'fun': lambda x: beq-np.dot(Aeq,x)},
            )
    return cons


def fitSpd(LED_DATA,LED_XYZ,STD_DATA_target,STD_DATA_XYZ_target,cctTarget,cdTarget):
    Aeq = LED_XYZ
    beq = STD_DATA_XYZ_target
    channel_num = LED_DATA.shape[1]
    x0 = np.ones((channel_num)) / channel_num
    cons = constraint_func(Aeq,beq)
    res = minimize(compute_func(LED_DATA,STD_DATA_target,channel_num,cctTarget,cdTarget), x0, method='SLSQP',constraints=cons)

    return res

import pickle

def save_variable(variable, filename='saved_variable.pkl'):
    """将变量保存到文件"""
    try:
        with open(filename, 'wb') as file:
            pickle.dump(variable, file)
        print(f"变量已成功保存到 {filename}")
    except Exception as e:
        print(f"保存变量时出错: {e}")

def load_variable(filename='saved_variable.pkl'):
    """从文件加载变量"""
    try:
        with open(filename, 'rb') as file:
            variable = pickle.load(file)
        return variable
    except FileNotFoundError:
        print(f"错误: 文件 {filename} 未找到")
        return None
    except Exception as e:
        print(f"加载变量时出错: {e}")
        return None

def m_matchSpd(ledData,stdData,accuracy,cdTarget,matchMethod):
    """
    LED_DATA:
        硬件传递LED光谱信息
    STD_DATA:
        目标光谱信息
    accuracy:
        精确度
    cdTarget:
        目标照度
    matchMethod:
        匹配方式
    """
    global CIE_RGB
    # save_variable(ledData,'ledData.pkl')
    # save_variable(stdData,'stdData.pkl')
    # save_variable(accuracy, 'accuracy.pkl')
    # save_variable(cdTarget, 'cdTarget.pkl')
    # save_variable(matchMethod, 'matchMethod.pkl')
    # ledData=load_variable('ledData.pkl')
    # stdData=load_variable('stdData.pkl')
    # accuracy=load_variable('accuracy.pkl')
    # cdTarget=load_variable('cdTarget.pkl')
    # matchMethod=load_variable('matchMethod.pkl')

    insertLEDData,insertSTDData,CIE_RGB,powerLedData = processData(ledData,stdData,accuracy,matchMethod)

    insertLED_XYZ = get_LED_XYZ(insertLEDData)
    insertSTD_XYZ = get_STD_XYZ(insertSTDData)
    # 计算目标光谱色温
    cctTarget = getCCT(insertSTDData)
    res = fitSpd(insertLEDData,insertLED_XYZ,insertSTDData,insertSTD_XYZ,cctTarget,cdTarget)
    # 得到a,tt
    a1 = np.maximum(res.x,0.0)
    a1 = np.minimum(a1,1.0)
    # tt = res.fun
    # 得到新spd
    newSpd = getNewSpd(insertLEDData,a1)
    powerSpd = getNewSpd(powerLedData, a1)

    #---------------------------------------------------#
    folder_path = './ledData/31通道'
    csv_files = [f for f in os.listdir(folder_path) if f.endswith('.csv')]

    a2_dict = {}

    for file in csv_files:
        file_path = os.path.join(folder_path, file)
        df = pd.read_csv(file_path, header=None)
        # 去除第一行
        data = df.iloc[1:, :]
        # 当前文件第一列的最大值
        first_col_max = data.iloc[:, 0].max()
        # 计算每列的a2
        for col_idx in range(data.shape[1]):
            col_max = data.iloc[:, col_idx].max()
            a2 = col_max / first_col_max if first_col_max != 0 else 0
            key = f"{file}_{col_idx}"
            a2_dict[key] = a2

    updated_a = []

    for idx, file in enumerate(csv_files):
        x_list = df.iloc[0, :24].values.astype(float) / 1000 # 横坐标
        a1_value = a1[idx]
        a2_list = [a2_dict[f"{file}_{col_idx}"] for col_idx in range(24)]
        # 排序a2
        sorted_a2 = sorted(a2_list)
        # 找到最接近a1的两个a2x和a2y
        a2x, a2y = None, None
        for i in range(len(sorted_a2) - 1):
            if sorted_a2[i] <= a1_value <= sorted_a2[i + 1]:
                a2x, a2y = sorted_a2[i], sorted_a2[i + 1]
                break
        # 边界处理
        if a2x is None or a2y is None:
            # 如果a1小于所有a2，取最小的两个
            if a1_value < sorted_a2[0]:
                a2x, a2y = sorted_a2[0], sorted_a2[1]
            # 如果a1大于所有a2，取最大的两个
            elif a1_value > sorted_a2[-1]:
                a2x, a2y = sorted_a2[-2], sorted_a2[-1]
            else:
                a2x = a2y = a1_value
        # 线性插值
        if a2x != a2y:
            idx_x = a2_list.index(a2x)
            idx_y = a2_list.index(a2y)
            x1 = x_list[idx_x]
            x2 = x_list[idx_y]
            interp_x = x1 + (a1_value - a2x) * (x2 - x1) / (a2y - a2x)
        else:
            interp_x = x_list[a2_list.index(a2x)]

        # # 找到a2x和a2y在a2_list中的索引
        # idx_x = a2_list.index(a2x)
        # idx_y = a2_list.index(a2y)
        # x1 = x_list[idx_x]
        # x2 = x_list[idx_y]
        # interp_x = x1 + (a1_value - a2x) * (x2 - x1) / (a2y - a2x)
        updated_a.append(interp_x)
        # # updated_a.append(0.1)

        a = np.array(updated_a)

    # updated_a即为更新后的a，包含31个值
    # ---------------------------------------------------#

    # 得到目标亮度比例
    scale = getCdScale(powerSpd,cdTarget)
    a1 = a1 * scale
    powerSpd = powerSpd * scale

    # 得到目标光谱色温
    T = getCCT(powerSpd)
    XYZ = getCIE_XYZ(powerSpd)
    x,y = getOneCIE_xy(XYZ)
    cd = computeCd(powerSpd)
    #todo:为了适配整体亮度调节
    # save_variable(a,'a.pkl')
    # save_variable(newSpd,'newSpd.pkl')
    # save_variable(insertLEDData, 'insertLEDData.pkl')
    # save_variable(insertSTDData, 'insertSTDData.pkl')
    # save_variable(T, 'T.pkl')
    # save_variable(XYZ,'XYZ.pkl')
    # save_variable(x,'x.pkl')
    # save_variable(y, 'y.pkl')
    # save_variable(cd, 'cd.pkl')
    # save_variable(scale, 'scale.pkl')
    # a=load_variable('a.pkl')
    # newSpd=load_variable('newSpd.pkl')
    # insertLEDData=load_variable('insertLEDData.pkl')
    # insertSTDData=load_variable('insertSTDData.pkl')
    # T=load_variable('T.pkl')
    # XYZ=load_variable('XYZ.pkl')
    # x=load_variable('x.pkl')
    # y=load_variable('y.pkl')
    # cd=load_variable('cd.pkl')
    # scale=load_variable('scale.pkl')
    return a,newSpd,insertLEDData,insertSTDData,T,XYZ,x,y,cd,scale, cctTarget
if __name__ == '__main__':
    m_matchSpd()