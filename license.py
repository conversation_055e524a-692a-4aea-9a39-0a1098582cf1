# 加密狗
import Psyunew3

import sys
from ctypes import *
import platform
# 加密狗

# from PyQt5 import QtCore, QtGui, QtWidgets

SIGN_LEN = (64 * 2 + 1)
SM2_ADDBYTE = 97
MAX_ENCLEN = 128
MAX_DECLEN = (MAX_ENCLEN + SM2_ADDBYTE)
SM2_USENAME_LEN = 80

KeyPath = create_string_buffer(260)

ret = c_int()
ver = c_int()
verEx = c_int()

ret = Psyunew3.FindPort(0, KeyPath)


def write_dog_statu():
    InString = '加密锁'.encode('utf-8')
    ret = Psyunew3.YWriteString(InString, 0, 'FFFFFFFF'.encode('utf-8'), 'FFFFFFFF'.encode('utf-8'),
                                KeyPath)
    if ret != 0:
        print('写字符串失败\n')
    else:
        print('写入成功。写入的字符串的长度是:%d\n' % (len(InString)))
    pass


def read_dog_stats():
    # 从加密锁中读取字符串,使用默认的读密码:ffffffff', 'ffffffff', 从加密锁的第0个地址开始读
    mylen = c_short()
    mylen = 28  # 注意这里的长度，长度要与写入的字符串的长度相同,
    outstring = create_string_buffer((mylen + 1))
    if Psyunew3.YReadString(outstring, 0, mylen, 'FFFFFFFF'.encode('utf-8'), 'FFFFFFFF'.encode('utf-8'), KeyPath) != 0:
       # print('读字符串失败\n')
        print('False')
        #return  "multiLight"
       # return "multiLight"
    else:
       # print('读字符串成功:%s\n' % (outstring.value))
       #  return outstring.value.decode('utf-8')
       return  "multiLight"

#  if read_dog_stats() == "True":
#     print("hello")