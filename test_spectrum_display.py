#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试光谱显示功能
验证光谱匹配算法和图形显示是否正常工作
"""

import numpy as np
import matplotlib.pyplot as plt
from scipy.interpolate import interp1d

def test_spectrum_matching():
    """测试光谱匹配功能"""
    print("🧪 测试光谱匹配功能...")
    
    # 创建模拟的目标光谱（D65标准光源）
    wavelengths = np.arange(350, 1101)
    
    # 简化的D65光谱数据
    d65_spectrum = np.exp(-((wavelengths - 550) / 100) ** 2) * 0.8 + \
                   np.exp(-((wavelengths - 460) / 80) ** 2) * 0.6 + \
                   np.exp(-((wavelengths - 650) / 120) ** 2) * 0.4
    
    # 归一化
    d65_spectrum = d65_spectrum / np.max(d65_spectrum)
    
    # 创建模拟的LED光谱数据（31通道）
    led_peaks = np.linspace(380, 780, 31)  # 31个LED峰值波长
    led_spectra = []
    
    for peak in led_peaks:
        # 每个LED的高斯光谱
        led_spectrum = np.exp(-((wavelengths - peak) / 20) ** 2)
        led_spectra.append(led_spectrum)
    
    led_spectra = np.array(led_spectra).T  # shape: (751, 31)
    
    # 简单的最小二乘法匹配
    # 只使用可见光范围 (400-700nm)
    visible_range = (wavelengths >= 400) & (wavelengths <= 700)
    target_visible = d65_spectrum[visible_range]
    led_visible = led_spectra[visible_range, :]
    
    # 非负最小二乘解
    from scipy.optimize import nnls
    weights, residual = nnls(led_visible, target_visible)
    
    # 计算匹配结果
    matched_spectrum = led_spectra @ weights
    
    # 显示结果
    plt.figure(figsize=(12, 8))
    
    plt.subplot(2, 2, 1)
    plt.plot(wavelengths, d65_spectrum, 'g-', linewidth=2, label='目标光谱 (D65)')
    plt.plot(wavelengths, matched_spectrum, 'b--', linewidth=2, label='匹配光谱')
    plt.xlabel('波长 (nm)')
    plt.ylabel('相对强度')
    plt.title('光谱匹配结果')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.subplot(2, 2, 2)
    plt.bar(range(len(weights)), weights, alpha=0.7)
    plt.xlabel('LED通道')
    plt.ylabel('权重')
    plt.title('LED通道权重分布')
    plt.grid(True, alpha=0.3)
    
    plt.subplot(2, 2, 3)
    error = np.abs(d65_spectrum - matched_spectrum)
    plt.plot(wavelengths, error, 'r-', linewidth=1)
    plt.xlabel('波长 (nm)')
    plt.ylabel('绝对误差')
    plt.title('匹配误差')
    plt.grid(True, alpha=0.3)
    
    plt.subplot(2, 2, 4)
    # 显示前10个LED的光谱
    for i in range(0, 31, 3):
        plt.plot(wavelengths, led_spectra[:, i] * weights[i], 
                alpha=0.6, label=f'LED{i+1}')
    plt.xlabel('波长 (nm)')
    plt.ylabel('加权强度')
    plt.title('主要LED贡献')
    plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()
    
    # 计算匹配质量指标
    mse = np.mean((d65_spectrum - matched_spectrum) ** 2)
    correlation = np.corrcoef(d65_spectrum, matched_spectrum)[0, 1]
    
    print(f"✅ 光谱匹配完成!")
    print(f"📊 匹配质量指标:")
    print(f"   - 均方误差 (MSE): {mse:.6f}")
    print(f"   - 相关系数: {correlation:.4f}")
    print(f"   - 活跃LED数量: {np.sum(weights > 0.01)}/31")
    print(f"   - 最大权重: {np.max(weights):.3f}")
    
    return weights, matched_spectrum, d65_spectrum

def test_color_calculation():
    """测试颜色计算功能"""
    print("\n🎨 测试颜色计算功能...")
    
    # CIE 1931 标准观察者函数 (简化版)
    wavelengths = np.arange(380, 781, 5)
    
    # 简化的XYZ匹配函数
    x_bar = np.exp(-((wavelengths - 600) / 50) ** 2) * 0.8
    y_bar = np.exp(-((wavelengths - 555) / 40) ** 2) * 1.0
    z_bar = np.exp(-((wavelengths - 450) / 30) ** 2) * 0.6
    
    # 测试光谱
    test_spectrum = np.exp(-((wavelengths - 500) / 60) ** 2)
    
    # 计算XYZ值
    X = np.trapz(test_spectrum * x_bar, wavelengths)
    Y = np.trapz(test_spectrum * y_bar, wavelengths)
    Z = np.trapz(test_spectrum * z_bar, wavelengths)
    
    # 归一化
    total = X + Y + Z
    if total > 0:
        x = X / total
        y = Y / total
        z = Z / total
    else:
        x = y = z = 0
    
    # 计算色温 (简化公式)
    if x != 0 and y != 0:
        n = (x - 0.3320) / (0.1858 - y)
        cct = 449 * n**3 + 3525 * n**2 + 6823.3 * n + 5520.33
    else:
        cct = 0
    
    print(f"✅ 颜色计算完成!")
    print(f"📊 颜色参数:")
    print(f"   - CIE x: {x:.4f}")
    print(f"   - CIE y: {y:.4f}")
    print(f"   - CIE z: {z:.4f}")
    print(f"   - 相关色温: {cct:.0f}K")
    
    return X, Y, Z, x, y, cct

if __name__ == "__main__":
    print("🔬 多光谱光源控制系统 - 功能测试")
    print("=" * 50)
    
    try:
        # 测试光谱匹配
        weights, matched, target = test_spectrum_matching()
        
        # 测试颜色计算
        X, Y, Z, x, y, cct = test_color_calculation()
        
        print("\n" + "=" * 50)
        print("🎉 所有测试完成!")
        print("✅ 光谱匹配算法正常工作")
        print("✅ 颜色计算功能正常工作")
        print("✅ 图形显示功能正常工作")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
