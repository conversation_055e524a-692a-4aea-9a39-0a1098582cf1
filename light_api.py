# _*_ coding:utf-8 _*_
# Group  :   ColorSpace
# Author :   <PERSON><PERSON><PERSON>
# Data   :   2023/5/26
# File   :   Light_API.py
# Tool   :   PyCharm
# Des    :   "光源指令的组合"


import time
import serial


base = [str(x) for x in range(10)] + [chr(x) for x in range(ord('A'), ord('A') + 6)]


def dec2hex(num):
    l = []
    if num < 0:
        return '-' + dec2hex(abs(num))
    while True:
        num, rem = divmod(num, 16)
        l.append(base[rem])
        if num == 0:
            return ''.join(l[::-1])


# 转16进制 2位
def hex_data2(data):
    result_data = ""
    if len(dec2hex(int(data))) == 1:
        result_data = "0" + dec2hex(int(data))
    elif len(dec2hex(int(data))) == 2:
        result_data = dec2hex(int(data))
    return result_data


def to_data2(data):
    result_data = ""
    data = str(data)
    if len(data) == 1:
        result_data = "0" + data
    elif len(data) == 2:
        result_data = data
    return result_data


# 转16进制 4位
def hex_data4(data):
    result_data = ""
    if len(dec2hex(int(data))) == 1:
        result_data = "000" + dec2hex(int(data))
    elif len(dec2hex(int(data))) == 2:
        result_data = "00" + dec2hex(int(data))
    elif len(dec2hex(int(data))) == 3:
        result_data = "0" + dec2hex(int(data))
    elif len(dec2hex(int(data))) == 4:
        result_data = dec2hex(int(data))
    new_result_data = result_data[0:2] + " " + result_data[2:]
    print('new_result_dataL:',new_result_data)
    return new_result_data


def connect(com_number="com1"):  # connect sys ok error
    global ser
    try:
        ser = serial.Serial(com_number, 9600, bytesize=8, parity='N', stopbits=1, timeout=0.01)  # 230400
    except:
        return False
    time.sleep(0.5)


def disconnect():
    time.sleep(0.5)
    ser.close()


def set_light(str_data=""):
    try:
        if str_data.find(" ") >= 0:
            # 转成16进制发送
            ser.write(bytes.fromhex(str_data))
        else:
            ser.write(str_data.encode("utf-8"))
        time.sleep(0.5)
    except:
        return False


def recv_data():
    for i in range(1000):
        if i >= 550:
            print("数据超时")
            return "False"
        print("等待数据接收...")
        # rec_data = ser.read(64)
        rec_data = ser.readall()
        if rec_data == b'':
            continue
        else:
            ret = rec_data.decode('utf-8')
            if ret.find("Received_Ready") >= 0:
                print("接收到：Received_Ready退出等待")
                return "Received_Ready"
            elif ret.find("Number_Of_Received_Data") >= 0:
                print("接收到：Number_Of_Received_Data退出等待")
                return "Number_Of_Received_Data"
            elif ret.find("Received_Error") >= 0:
                return "Received_Error"
            print("接收到数据,继续等待", ret)


# 组合指令
def command_type(option=None, parm=None):
    """
    根据指令种类，组合指令
    :param option:指令种类
    :param parm: 元组类型参数
    :return:发送的指令
    """

    if option == "关闭":
        send_data = "FF 00 00 00 00 01"
    elif option == "打开":
        send_data = "C0 00 00 00 00 01"
    elif option == "可见光(修改当前)":
        # 可见光(修改当前)	固定	色温高位	色温低位	照度高位	照度低位
        send_data = f"C1 00 {hex_data4(parm[0])} {hex_data4(parm[1])}"
    elif option == "红外(修改当前)":
        # 红外(修改当前)	固定	红外850高位	红外850低位	红外940高位	红外940低位
        send_data = f"C2 00 {hex_data4(parm[0])} {hex_data4(parm[1])}"
    elif option == "flicker(修改当前)":
        # flicker(修改当前)	固定	频率高位	频率低位	占空比高位	占空比低位
        send_data = f"C3 00 {hex_data4(parm[0])} {hex_data4(parm[1])}"
    elif option == "低照(修改当前)":
        # 低照(修改当前)	固定	色温高位	色温低位	照度高位	照度低位
        send_data = f"C4 00 {hex_data4(parm[0])} {hex_data4(parm[1])}"
    elif option == "打开(组别调用)":
        # 打开(组别调用)	组别	固定	固定	固定	固定
        send_data = f"D0 {hex_data2(parm[0])} 00 00 00 01"
    elif option == "可见光(组内修改)":
        # 可见光(组内修改)	组别	色温高位	色温低位	照度高位	照度低位
        send_data = f"C1 {hex_data2(parm[0])} {hex_data4(parm[1])} {hex_data4(parm[2])}"
    elif option == "红外(组内修改)":
        # 红外(组内修改)	组别	红外850高位	红外850低位	红外940高位	红外940低位
        send_data = f"C2 {hex_data2(parm[0])} {hex_data4(parm[1])} {hex_data4(parm[2])}"
    elif option == "flicker(组内修改)":
        # flicker(组内修改)	组别	频率高位	频率低位	占空比高位	占空比低位
        send_data = f"C3 {hex_data2(parm[0])} {hex_data4(parm[1])} {hex_data4(parm[2])}"
    elif option == "低照(组内修改)":
        # 低照(组内修改)	组别	色温高位	色温低位	照度高位	照度低位
        send_data = f"C4 {hex_data2(parm[0])} {hex_data4(parm[1])} {hex_data4(parm[2])}"
    elif option == "数据返回开关":
        # 数据返回开关	固定	固定	固定	固定	开/关
        send_data = f"B1 00 00 00 00 {hex_data2(parm[0])}"
    elif option == "蜂鸣器开关":
        # 蜂鸣器开关	固定	固定	固定	固定	开/关
        send_data = f"B2 00 00 00 00 {hex_data2(parm[0])}"
    elif option == "触摸屏亮度设置":
        # 触摸屏亮度设置	固定	固定	固定	固定	亮度1-100
        send_data = f"B3 00 00 00 00 {hex_data2(parm[0])}"
    elif option == "光源地址设置":
        # 光源地址设置	固定	固定	固定	固定	地址号
        send_data = f"B4 00 00 00 00 {hex_data2(parm[0])}"
    elif option == "联控等待时间":
        # 联控等待时间	固定	固定	固定	固定	毫秒
        send_data = f"B5 00 00 00 00 {hex_data2(parm[0])}"
    elif option == "定时关闭开关":
        # 定时关闭开关	固定	固定	固定	固定	开/关
        send_data = f"B6 00 00 00 00 {hex_data2(parm[0])}"
    elif option == "定时时间设置":
        # 定时时间设置	固定	固定	固定	固定	分钟
        send_data = f"B7 00 00 00 00 {hex_data2(parm[0])}"
    else:
        send_data = ""
        # print("指令种类没有预设")
    # print("指令种类",option)
    # print("发送数据",send_data)
    return send_data + " "


# 多台控制
def multi_controls(adrs=["01", "02", "03"],
                   options=[["关闭", ()], ["可见光(修改当前)", ("100", "200")], ["可见光(组内修改)", ("1", "100", "200")]]):
    """
    多台光源控制
    :param nums: 光源控制数量
    :param adrs: 光源控制的地址
    :param options: 组合指令
    :return:
    """
    nums = len(adrs)
    send_data = f"1A A1 {hex_data2(nums)} " + " ".join(adrs) + " "
    # 加上数据长度
    send_data += hex_data4(nums * 6) + " "
    for option in options:
        if option[0] not in ["关闭","打开"]:
            ret_data = command_type(option[0], option[1])
        else:
            ret_data = command_type(option[0])
        send_data += ret_data
    # 加上 校验码 ff-sum(第一位+校验码前一位，取和的最后一个字节)
    check_sum = 0
    for i in send_data.split(" "):
        if i == "":
            continue
        s = int(str(i), base=16)
        check_sum += s
    b = str(hex(check_sum))[-2:]
    a = 255 - int(b, 16)
    # 转成 16进制
    check_sum = hex_data2(a)
    send_data += check_sum
    print("------------send_data--------", send_data)
    set_light(send_data)
    return send_data
    # tcp_send_data(send_data)


def crc_data(send_data):
    check_sum = 0
    for i in send_data.split(" "):
        if i == "":
            continue
        s = int(str(i), base=16)
        check_sum += s
    b = str(hex(check_sum))[-2:]
    a = 255 - int(b, 16)
    # 转成 16进制
    check_sum = hex_data2(a)
    #print(check_sum)
    send_data = send_data + ' ' + check_sum
    print("------------send_data--------", send_data)
    return send_data
   # set_light(send_data)


def set_low_mode(id,pwm1,pwm2):
    send_data = "1A A1 01 "+ id + " 00 06 C4 00 " + hex_data4(pwm1) +' '+ hex_data4(pwm2)
    #send_data = "1A A1 01 01 00 06 C0 00 00 00 00 01"
    data = crc_data(send_data)
    print('data',data)
    set_light(data)
    #1A A1 01 1e 00 06 C0 00 00 00 00 01 5E


def set_color(id,type):
    if type == "B":
        send_data = "1A A1 01 " + id + " 00 06 C5 00 00 01 00 01"
        data = crc_data(send_data)
        print('data',data)
        set_light(data)
    elif type =="H":
        send_data = "1A A1 01 " + id + " 00 06 C5 00 00 00 00 00"
        data = crc_data(send_data)
        print('data', data)
        set_light(data)


if __name__ == '__main__':#dd FF-22
    connect('com36')
    set_color('1C', 'H')

    #print(set_low_mode('1c',64,64))
    #connect('com45')
    # multi_controls(nums=8, adrs=["13", "21", "11","10", "14", "06", "20", "19"],
    #                options=[["关闭"],["关闭"],["关闭"] ,["关闭"],
    #                          ["关闭"],["guan"],["关闭"] ,["关闭"]
    #            multi_controls             ])

    # 13 0d
   # multi_controls(adrs=["1e",'07','0d'], options=[["打开"],["打开"],["打开"]])

    #multi_controls(adrs=["1e", '07', '0d'], options=[["关闭"], ["关闭"], ["关闭"]])


    #multi_controls(adrs=["1e"],options=[["关闭"]])

    '''
    multi_controls(adrs=["1e",'07','0d'],
                   options=[
                       ["可见光(修改当前)",("2800", "1000")],["可见光(修改当前)",("6500", "2000")],
                       ["可见光(修改当前)",("5000", "500")]])
    '''

    # multi_controls(adrs=["13"],
    #                options=[["打开"]])
    #
    #
    # multi_controls(adrs=["13"],
    #                options=[["关闭"]])
    #
    #
    # command_type("可见光(修改当前)",)
    # connect()
    # 13, 21, 11, 10, 14, 06, 20, 19&打开,打开,打开,打开,打开,打开,打开,打开
    # 13, 21, 11, 10, 14, 06, 20, 19&关闭,关闭,关闭,关闭,关闭,关闭,关闭,关闭
    # 13, 21, 11, 10, 14, 06, 20, 19&可见光(修改当前)可见光(修改当前)-6500-2000,可见光(修改当前)-6500-2000,可见光(修改当前)-6500-2000,可见光(修改当前)-6500-2000,可见光(修改当前)-6500-2000,可见光(修改当前)-6500-2000,可见光(修改当前)-6500-2000,可见光(修改当前)-6500-2000
    # set_light("OFF")
    # ret = recv_data()
    # set_light("SET")
    # set_light("ON")
    # set_light("ON")
    # set_light("L500") # 亮度
    # set_light("OFF")
    # set_light("C1000") # 色温
